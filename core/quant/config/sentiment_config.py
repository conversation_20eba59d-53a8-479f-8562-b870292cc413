# -*- coding: UTF-8 -*-

"""
情感分析配置文件

包含情感分析相关的所有配置参数
"""

from typing import Dict, List, Any


class SentimentConfig:
    """情感分析配置类"""
    
    # 权重配置
    SENTIMENT_WEIGHTS = {
        'base_weight': 0.4,        # SnowNLP基础权重
        'dict_weight': 0.4,        # 词典匹配权重
        'context_factor': 0.2      # 上下文调整权重
    }
    
    # 金融关键词权重映射
    FINANCIAL_KEYWORDS_WEIGHTS = {
        # 强烈积极
        '涨停': 1.4,
        '暴涨': 1.3,
        '大涨': 1.2,
        '强势': 1.2,
        '突破': 1.2,
        '利好': 1.2,
        '买入': 1.1,
        '上涨': 1.1,
        '看好': 1.1,
        '推荐': 1.1,
        
        # 强烈消极
        '跌停': 0.6,
        '暴跌': 0.7,
        '大跌': 0.8,
        '破位': 0.8,
        '利空': 0.8,
        '卖出': 0.9,
        '下跌': 0.9,
        '看空': 0.9,
        '风险': 0.9,
        '谨慎': 0.9,
        
        # 中性调整
        '震荡': 1.0,
        '整理': 1.0,
        '观望': 1.0,
        '等待': 1.0,
        '横盘': 1.0,
    }
    
    # 情感分类阈值
    SENTIMENT_THRESHOLDS = {
        'very_positive': 0.8,      # 非常积极
        'positive': 0.6,           # 积极
        'neutral_high': 0.55,      # 中性偏积极
        'neutral_low': 0.45,       # 中性偏消极
        'negative': 0.4,           # 消极
        'very_negative': 0.2       # 非常消极
    }
    
    # 准确性验证配置
    VALIDATION_CONFIG = {
        'error_threshold': 0.2,    # 误差阈值
        'sample_size': 100,        # 默认样本大小
        'test_data_days': 7        # 测试数据天数
    }
    
    # 文本预处理配置
    TEXT_PROCESSING = {
        'min_length': 5,           # 最小文本长度
        'max_length': 1000,        # 最大文本长度
        'remove_duplicates': True,  # 是否去重
        'clean_html': True         # 是否清理HTML
    }
    
    # 批量处理配置
    BATCH_PROCESSING = {
        'batch_size': 50,          # 批处理大小
        'max_workers': 4,          # 最大工作线程数
        'timeout': 30              # 超时时间（秒）
    }
    
    # 模型配置
    MODEL_CONFIG = {
        'finance_model_path': 'finance_sentiment.marshal',
        'fallback_to_default': True,
        'model_cache_size': 1000
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'log_sentiment_details': False,  # 是否记录详细情感分析日志
        'log_performance': True,         # 是否记录性能日志
        'log_errors': True              # 是否记录错误日志
    }
    
    @classmethod
    def get_sentiment_label(cls, score: float) -> str:
        """
        根据分数获取情感标签
        
        :param score: 情感分数 (0-1)
        :return: 情感标签
        """
        thresholds = cls.SENTIMENT_THRESHOLDS
        
        if score >= thresholds['very_positive']:
            return '非常积极'
        elif score >= thresholds['positive']:
            return '积极'
        elif score >= thresholds['neutral_high']:
            return '中性偏积极'
        elif score >= thresholds['neutral_low']:
            return '中性偏消极'
        elif score >= thresholds['negative']:
            return '消极'
        else:
            return '非常消极'
    
    @classmethod
    def get_keyword_weight(cls, keyword: str) -> float:
        """
        获取关键词权重
        
        :param keyword: 关键词
        :return: 权重值
        """
        return cls.FINANCIAL_KEYWORDS_WEIGHTS.get(keyword, 1.0)
    
    @classmethod
    def is_valid_text(cls, text: str) -> bool:
        """
        检查文本是否有效
        
        :param text: 文本内容
        :return: 是否有效
        """
        if not text or not isinstance(text, str):
            return False
        
        text_len = len(text.strip())
        config = cls.TEXT_PROCESSING
        
        return config['min_length'] <= text_len <= config['max_length']
    
    @classmethod
    def get_config_summary(cls) -> Dict[str, Any]:
        """
        获取配置摘要
        
        :return: 配置摘要
        """
        return {
            'sentiment_weights': cls.SENTIMENT_WEIGHTS,
            'keyword_count': len(cls.FINANCIAL_KEYWORDS_WEIGHTS),
            'thresholds': cls.SENTIMENT_THRESHOLDS,
            'validation': cls.VALIDATION_CONFIG,
            'text_processing': cls.TEXT_PROCESSING,
            'batch_processing': cls.BATCH_PROCESSING,
            'model_config': cls.MODEL_CONFIG
        }


# 创建全局配置实例
sentiment_config = SentimentConfig()


# 便捷函数
def get_sentiment_weights() -> Dict[str, float]:
    """获取情感权重配置"""
    return sentiment_config.SENTIMENT_WEIGHTS.copy()


def get_financial_keywords() -> Dict[str, float]:
    """获取金融关键词权重"""
    return sentiment_config.FINANCIAL_KEYWORDS_WEIGHTS.copy()


def get_sentiment_thresholds() -> Dict[str, float]:
    """获取情感分类阈值"""
    return sentiment_config.SENTIMENT_THRESHOLDS.copy()


def classify_sentiment(score: float) -> str:
    """分类情感分数"""
    return sentiment_config.get_sentiment_label(score)


def validate_text(text: str) -> bool:
    """验证文本有效性"""
    return sentiment_config.is_valid_text(text)


# 配置验证
def validate_config() -> bool:
    """
    验证配置的有效性
    
    :return: 配置是否有效
    """
    try:
        # 检查权重总和
        weights = sentiment_config.SENTIMENT_WEIGHTS
        weight_sum = weights['base_weight'] + weights['dict_weight']
        if not (0.8 <= weight_sum <= 1.0):
            print(f"警告: 权重总和异常 {weight_sum}")
            return False
        
        # 检查阈值顺序
        thresholds = sentiment_config.SENTIMENT_THRESHOLDS
        threshold_values = [
            thresholds['very_negative'],
            thresholds['negative'],
            thresholds['neutral_low'],
            thresholds['neutral_high'],
            thresholds['positive'],
            thresholds['very_positive']
        ]
        
        if threshold_values != sorted(threshold_values):
            print("警告: 情感阈值顺序错误")
            return False
        
        # 检查关键词权重范围
        for keyword, weight in sentiment_config.FINANCIAL_KEYWORDS_WEIGHTS.items():
            if not (0.1 <= weight <= 2.0):
                print(f"警告: 关键词 '{keyword}' 权重异常: {weight}")
                return False
        
        print("配置验证通过")
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


if __name__ == "__main__":
    # 验证配置
    validate_config()
    
    # 显示配置摘要
    import json
    summary = sentiment_config.get_config_summary()
    print("\n配置摘要:")
    print(json.dumps(summary, indent=2, ensure_ascii=False))
