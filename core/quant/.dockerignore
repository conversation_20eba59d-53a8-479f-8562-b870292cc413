# Blackbear 量化交易系统 Docker 构建忽略文件

# Python 缓存和编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/
pip-log.txt
pip-delete-this-directory.txt

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace
*.code-workspace

# 日志文件
*.log
logs/
*.out
task_manager.log

# 数据文件和缓存
data/
*.db
*.sqlite
*.sqlite3
file/
*.csv
*.xlsx
*.xls
backup/
reports/
*.marshal.*

# 临时文件和缓存
tmp/
temp/
.tmp/
.cache/
.pytest_cache/
.coverage
htmlcov/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# Git 文件
.git/
.gitignore
.gitattributes

# Docker 文件
Dockerfile*
docker-compose*.yml
.dockerignore

# 文档文件
README*.md
CHANGELOG*.md
LICENSE*
docs/
DETAILED_DOCUMENTATION.md

# 配置文件（可能包含敏感信息）
*.env
.env.*
config/local_*
secrets/
*.key
*.pem
*.crt

# 测试文件
tests/
test_*
*_test.py
pytest.ini
.pytest_cache/
.coverage
htmlcov/
.tox/

# 部署和构建脚本
deploy*.sh
install*.sh
build*.sh
online.sh
test-build.sh
run_*.sh

# TA-Lib 源码和编译文件（Docker中会重新下载编译）
ta-lib/
ta-lib-*.tar.gz
*.tar.gz

# 数据库相关
mysql/
redis/

# 任务调度器运行时文件
gocron/work/
*.pid

# 机器学习模型文件（如果很大的话）
*.model
*.pkl
*.joblib
*.h5

# 数据文件
*.csv
*.json
*.parquet
*.feather
enhanced_pulse_groups.csv
pulse_groups.csv
zjtx.txt
test.pdf

# 开发工具配置
.mypy_cache/
.black
.flake8
.isort.cfg
setup.cfg
tox.ini

# Jupyter Notebook
.ipynb_checkpoints/
*.ipynb

# 其他临时文件
*.bak
*.backup
*.orig
*.tmp
*.swp
*.swo

# Node.js 文件（如果存在）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 排除特定文件但保留必要的配置文件
*.txt
!requirements.txt

# 性能分析文件
*.prof
*.pstats

# 编译器和构建工具生成的文件
*.o
*.a
*.la
*.lo
Makefile
configure
config.*
autom4te.cache/
libtool
install-sh
missing
depcomp
aclocal.m4

# 排除示例和演示文件
examples/
demo/
sample/
