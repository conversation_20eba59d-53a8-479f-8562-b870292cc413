#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
情感分析改进效果测试脚本

快速测试改进后的情感分析效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from tasks.daily_multiple.market_text_mining import MarketTextMiner
from config.sentiment_config import classify_sentiment
from utils.logger import logger


def test_sentiment_examples():
    """测试情感分析示例"""
    
    # 创建文本挖掘器实例
    miner = MarketTextMiner()
    
    # 测试用例
    test_cases = [
        "股价大涨突破重要阻力位，买入信号强烈，后市看好",
        "利空消息打击，股价暴跌，建议谨慎操作",
        "市场震荡整理，等待方向选择，建议观望",
        "涨停板封单强劲，资金大量流入，技术面突破",
        "跌停板打开，抛盘汹涌，技术面破位下行",
        "业绩大幅增长，盈利能力提升，投资价值显现",
        "财务造假曝光，监管介入调查，风险巨大",
        "政策利好频出，行业景气度提升，龙头受益",
        "贸易摩擦升级，出口压力加大，业绩承压",
        "新产品发布成功，市场反响热烈，订单饱满"
    ]
    
    print("=" * 80)
    print("情感分析测试结果")
    print("=" * 80)
    print(f"{'序号':<4} {'情感分数':<8} {'情感分类':<12} {'文本内容':<50}")
    print("-" * 80)
    
    for i, text in enumerate(test_cases, 1):
        try:
            # 分析情感
            positive_prob, negative_prob = miner.analyze_sentiment(text)
            
            if positive_prob is not None:
                sentiment_label = classify_sentiment(positive_prob)
                print(f"{i:<4} {positive_prob:<8.3f} {sentiment_label:<12} {text[:47]:<50}")
            else:
                print(f"{i:<4} {'N/A':<8} {'分析失败':<12} {text[:47]:<50}")
                
        except Exception as e:
            print(f"{i:<4} {'ERROR':<8} {'异常':<12} {text[:47]:<50}")
            logger.error(f"分析文本失败: {e}")
    
    print("-" * 80)


def test_comparison_with_original():
    """与原始方法对比测试"""
    
    from snownlp import SnowNLP
    from utils.helper import clear_html
    
    miner = MarketTextMiner()
    
    # 对比测试用例
    comparison_cases = [
        "涨停板，强势突破，买入机会",
        "跌停板，破位下行，卖出信号", 
        "震荡整理，等待突破方向",
        "利好消息刺激，股价大涨",
        "利空打击，股价暴跌"
    ]
    
    print("\n" + "=" * 100)
    print("原始方法 vs 改进方法对比")
    print("=" * 100)
    print(f"{'文本':<30} {'原始分数':<10} {'改进分数':<10} {'原始分类':<12} {'改进分类':<12} {'改进幅度':<10}")
    print("-" * 100)
    
    for text in comparison_cases:
        try:
            # 原始SnowNLP方法
            clean_text = clear_html(text)
            s = SnowNLP(clean_text)
            original_score = s.sentiments
            original_label = classify_sentiment(original_score)
            
            # 改进方法
            improved_score, _ = miner.analyze_sentiment(text)
            
            if improved_score is not None:
                improved_label = classify_sentiment(improved_score)
                improvement = improved_score - original_score
                
                print(f"{text[:28]:<30} {original_score:<10.3f} {improved_score:<10.3f} "
                      f"{original_label:<12} {improved_label:<12} {improvement:+.3f}")
            else:
                print(f"{text[:28]:<30} {original_score:<10.3f} {'N/A':<10} "
                      f"{original_label:<12} {'分析失败':<12} {'N/A':<10}")
                
        except Exception as e:
            print(f"{text[:28]:<30} {'ERROR':<10} {'ERROR':<10} "
                  f"{'异常':<12} {'异常':<12} {'N/A':<10}")
            logger.error(f"对比测试失败: {e}")
    
    print("-" * 100)


def test_real_data_sample():
    """测试真实数据样本"""
    
    miner = MarketTextMiner()
    
    try:
        print("\n" + "=" * 60)
        print("真实数据情感分析测试")
        print("=" * 60)
        
        # 获取少量真实数据进行测试
        df_media = miner.fetch_media_data(days=1)
        
        if df_media.empty or 'content' not in df_media.columns:
            print("没有可用的媒体数据进行测试")
            return
        
        # 取前5条数据进行测试
        sample_data = df_media.head(5)
        
        print(f"{'序号':<4} {'情感分数':<10} {'情感分类':<12} {'文本摘要':<40}")
        print("-" * 70)
        
        for i, (_, row) in enumerate(sample_data.iterrows(), 1):
            content = row.get('content', '')
            if content and isinstance(content, str):
                positive_prob, _ = miner.analyze_sentiment(content)
                
                if positive_prob is not None:
                    sentiment_label = classify_sentiment(positive_prob)
                    # 显示文本摘要（前40个字符）
                    summary = content.replace('\n', ' ').strip()[:37] + "..."
                    print(f"{i:<4} {positive_prob:<10.3f} {sentiment_label:<12} {summary:<40}")
                else:
                    print(f"{i:<4} {'N/A':<10} {'分析失败':<12} {'无法分析':<40}")
        
        print("-" * 70)
        
    except Exception as e:
        print(f"真实数据测试失败: {e}")
        logger.error(f"真实数据测试异常: {e}")


def main():
    """主函数"""
    try:
        print("开始情感分析改进效果测试...")
        
        # 测试1: 基础示例测试
        test_sentiment_examples()
        
        # 测试2: 与原始方法对比
        test_comparison_with_original()
        
        # 测试3: 真实数据测试
        test_real_data_sample()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("\n建议:")
        print("1. 如果改进效果明显，可以部署到生产环境")
        print("2. 如果效果不理想，可以调整配置参数")
        print("3. 可以运行完整的验证工具获取详细报告:")
        print("   python tasks/tools/sentiment_validator.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试执行失败: {e}")
        logger.error(f"测试主函数异常: {e}")


if __name__ == "__main__":
    main()
