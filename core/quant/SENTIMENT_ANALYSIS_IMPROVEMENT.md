# 舆情系统情感分析改进方案

## 问题分析

### 原有问题
1. **依赖通用模型**: 使用SnowNLP默认模型，对金融领域理解不够准确
2. **缺乏领域定制**: 没有针对金融文本的特殊处理
3. **简单二分类**: 只有积极/消极，缺乏细粒度分类
4. **忽略上下文**: 没有考虑金融语境的特殊性

### 影响
- 情感分数不准确，可能误导投资决策
- 舆情分析结果可靠性降低
- 系统整体分析质量受影响

## 改进方案

### 1. 多方法融合分析

#### 原始方法
```python
s = SnowNLP(clean_text)
positive_prob = s.sentiments
negative_prob = 1 - positive_prob
```

#### 改进方法
```python
# 方法1: SnowNLP基础分析
base_positive_prob = s.sentiments

# 方法2: 金融词典匹配分析
dict_sentiment = self._analyze_sentiment_by_dictionary(clean_text)

# 方法3: 上下文权重调整
context_weight = self._calculate_context_weight(clean_text)

# 综合计算最终情感分数
final_positive_prob = self._combine_sentiment_scores(
    base_positive_prob, dict_sentiment, context_weight
)
```

### 2. 金融词典匹配

- 使用专门的金融正面/负面词汇库
- 基于词汇出现频率计算情感倾向
- 文件位置: `utils/positive.txt`, `utils/negative.txt`

### 3. 上下文权重调整

#### 金融关键词权重映射
```python
FINANCIAL_KEYWORDS_WEIGHTS = {
    # 强烈积极
    '涨停': 1.4, '暴涨': 1.3, '大涨': 1.2,
    '突破': 1.2, '利好': 1.2, '买入': 1.1,
    
    # 强烈消极  
    '跌停': 0.6, '暴跌': 0.7, '大跌': 0.8,
    '破位': 0.8, '利空': 0.8, '卖出': 0.9,
    
    # 中性调整
    '震荡': 1.0, '整理': 1.0, '观望': 1.0
}
```

### 4. 配置化管理

#### 权重配置
```python
SENTIMENT_WEIGHTS = {
    'base_weight': 0.4,        # SnowNLP基础权重40%
    'dict_weight': 0.4,        # 词典匹配权重40%
    'context_factor': 0.2      # 上下文调整权重20%
}
```

#### 情感分类阈值
```python
SENTIMENT_THRESHOLDS = {
    'very_positive': 0.8,      # 非常积极
    'positive': 0.6,           # 积极
    'neutral_high': 0.55,      # 中性偏积极
    'neutral_low': 0.45,       # 中性偏消极
    'negative': 0.4,           # 消极
    'very_negative': 0.2       # 非常消极
}
```

## 文件结构

```
core/quant/
├── tasks/daily_multiple/
│   └── market_text_mining.py          # 主要改进文件
├── tasks/tools/
│   ├── sentiment_validator.py         # 验证工具
│   └── public_opinion_analyzer.py     # 舆情分析器
├── config/
│   └── sentiment_config.py            # 配置文件
├── utils/
│   ├── positive.txt                   # 正面词汇库
│   └── negative.txt                   # 负面词汇库
└── test_sentiment_improvement.py      # 测试脚本
```

## 使用方法

### 1. 快速测试
```bash
cd core/quant
python test_sentiment_improvement.py
```

### 2. 完整验证
```bash
cd core/quant
python tasks/tools/sentiment_validator.py
```

### 3. 配置调整
编辑 `config/sentiment_config.py` 文件调整参数

### 4. 生产部署
改进后的代码已集成到原有系统中，无需修改调用方式

## 预期效果

### 准确性提升
- 金融文本情感识别准确率提升20-30%
- 减少误判，特别是金融专业术语的理解

### 细粒度分类
- 从简单的积极/消极扩展到6级分类
- 更好地反映情感强度差异

### 可配置性
- 支持权重调整和阈值配置
- 便于根据实际效果进行优化

## 验证指标

### 准确性指标
- 测试用例准确率
- 平均误差
- 误差标准差

### 分布指标
- 正面情感比例
- 负面情感比例
- 中性情感比例

### 改进指标
- 与原方法的对比改进幅度
- 显著改进案例数量

## 后续优化建议

### 1. 扩展词典
- 持续收集金融领域专业词汇
- 根据实际使用效果调整词汇权重

### 2. 机器学习模型
- 考虑训练专门的金融情感分析模型
- 使用BERT等预训练模型进行微调

### 3. 实时反馈
- 建立人工标注机制
- 根据反馈持续优化算法

### 4. A/B测试
- 在生产环境中进行对比测试
- 量化改进效果

## 注意事项

### 1. 兼容性
- 保持原有API接口不变
- 确保向后兼容

### 2. 性能
- 新增的处理步骤可能略微增加计算时间
- 通过批处理和缓存优化性能

### 3. 维护
- 定期更新词典和配置
- 监控分析效果和系统性能

## 联系方式

如有问题或建议，请联系开发团队。
