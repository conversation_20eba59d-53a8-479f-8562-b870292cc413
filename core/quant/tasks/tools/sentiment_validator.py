# -*- coding: UTF-8 -*-

"""
情感分析验证工具

用于测试和验证情感分析的准确性
提供多种测试方法和评估指标
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from utils.logger import logger
from tasks.daily_multiple.market_text_mining import MarketTextMiner


class SentimentValidator:
    """情感分析验证器"""
    
    def __init__(self):
        self.miner = MarketTextMiner()
        self.test_cases = []
        self._load_test_cases()
    
    def _load_test_cases(self):
        """加载测试用例"""
        # 金融文本测试用例 (文本, 预期积极概率)
        self.test_cases = [
            # 明显积极的文本
            ("股价大涨，突破重要阻力位，买入信号强烈", 0.8),
            ("利好消息频出，公司业绩大幅增长，强烈推荐", 0.9),
            ("涨停板，资金大量流入，后市看好", 0.85),
            ("技术面突破，成交量放大，上涨趋势确立", 0.8),
            
            # 明显消极的文本
            ("股价暴跌，跌破重要支撑位，建议卖出", 0.2),
            ("利空消息打击，公司业绩大幅下滑，谨慎操作", 0.1),
            ("跌停板，资金大量流出，后市堪忧", 0.15),
            ("技术面破位，成交量萎缩，下跌趋势明确", 0.2),
            
            # 中性文本
            ("股价横盘整理，等待方向选择", 0.5),
            ("市场观望情绪浓厚，成交量一般", 0.5),
            ("技术指标显示震荡格局，建议观望", 0.5),
            ("消息面平静，市场缺乏热点", 0.5),
            
            # 复杂语境
            ("虽然短期下跌，但长期趋势依然向好", 0.6),
            ("利空出尽是利好，底部区域值得关注", 0.65),
            ("调整充分后有望迎来反弹机会", 0.6),
            ("风险释放完毕，投资价值逐步显现", 0.65),
        ]
    
    def validate_sentiment_accuracy(self) -> Dict[str, float]:
        """
        验证情感分析准确性
        
        :return: 验证结果统计
        """
        try:
            logger.info("开始验证情感分析准确性")
            
            results = []
            for text, expected_score in self.test_cases:
                # 获取分析结果
                positive_prob, negative_prob = self.miner.analyze_sentiment(text)
                
                if positive_prob is not None:
                    # 计算误差
                    error = abs(positive_prob - expected_score)
                    results.append({
                        'text': text,
                        'expected': expected_score,
                        'actual': positive_prob,
                        'error': error,
                        'accurate': error <= 0.2  # 误差在0.2以内认为准确
                    })
                    
                    logger.debug(f"文本: {text[:30]}... 预期: {expected_score:.2f} 实际: {positive_prob:.2f} 误差: {error:.2f}")
            
            # 计算统计指标
            if results:
                df = pd.DataFrame(results)
                stats = {
                    'total_cases': len(results),
                    'accurate_cases': df['accurate'].sum(),
                    'accuracy_rate': df['accurate'].mean(),
                    'average_error': df['error'].mean(),
                    'max_error': df['error'].max(),
                    'min_error': df['error'].min(),
                    'std_error': df['error'].std()
                }
                
                logger.info(f"验证完成 - 准确率: {stats['accuracy_rate']:.2%}, 平均误差: {stats['average_error']:.3f}")
                return stats
            else:
                logger.warning("没有有效的验证结果")
                return {}
                
        except Exception as e:
            logger.error(f"验证情感分析准确性失败: {e}")
            return {}
    
    def test_real_data_sentiment(self, sample_size: int = 100) -> Dict[str, any]:
        """
        测试真实数据的情感分析
        
        :param sample_size: 样本数量
        :return: 测试结果
        """
        try:
            logger.info(f"开始测试真实数据情感分析，样本数量: {sample_size}")
            
            # 获取真实媒体数据
            df_media = self.miner.fetch_media_data(days=7)
            
            if df_media.empty or 'content' not in df_media.columns:
                logger.warning("没有可用的媒体数据")
                return {}
            
            # 随机采样
            sample_data = df_media.sample(min(sample_size, len(df_media)))
            
            sentiment_scores = []
            for _, row in sample_data.iterrows():
                content = row['content']
                if content and isinstance(content, str):
                    positive_prob, negative_prob = self.miner.analyze_sentiment(content)
                    if positive_prob is not None:
                        sentiment_scores.append(positive_prob)
            
            if sentiment_scores:
                stats = {
                    'sample_size': len(sentiment_scores),
                    'mean_sentiment': np.mean(sentiment_scores),
                    'std_sentiment': np.std(sentiment_scores),
                    'positive_ratio': sum(1 for s in sentiment_scores if s > 0.6) / len(sentiment_scores),
                    'negative_ratio': sum(1 for s in sentiment_scores if s < 0.4) / len(sentiment_scores),
                    'neutral_ratio': sum(1 for s in sentiment_scores if 0.4 <= s <= 0.6) / len(sentiment_scores),
                    'min_sentiment': min(sentiment_scores),
                    'max_sentiment': max(sentiment_scores)
                }
                
                logger.info(f"真实数据测试完成 - 平均情感: {stats['mean_sentiment']:.3f}, 正面比例: {stats['positive_ratio']:.2%}")
                return stats
            else:
                logger.warning("没有有效的情感分析结果")
                return {}
                
        except Exception as e:
            logger.error(f"测试真实数据情感分析失败: {e}")
            return {}
    
    def compare_methods(self) -> Dict[str, any]:
        """
        比较不同情感分析方法的效果
        
        :return: 比较结果
        """
        try:
            logger.info("开始比较不同情感分析方法")
            
            comparison_results = []
            
            for text, expected_score in self.test_cases:
                # 原始SnowNLP方法
                from snownlp import SnowNLP
                from utils.helper import clear_html
                
                clean_text = clear_html(text)
                s = SnowNLP(clean_text)
                original_score = s.sentiments
                
                # 改进后的方法
                improved_score, _ = self.miner.analyze_sentiment(text)
                
                if improved_score is not None:
                    comparison_results.append({
                        'text': text,
                        'expected': expected_score,
                        'original': original_score,
                        'improved': improved_score,
                        'original_error': abs(original_score - expected_score),
                        'improved_error': abs(improved_score - expected_score),
                        'improvement': abs(original_score - expected_score) - abs(improved_score - expected_score)
                    })
            
            if comparison_results:
                df = pd.DataFrame(comparison_results)
                
                stats = {
                    'original_avg_error': df['original_error'].mean(),
                    'improved_avg_error': df['improved_error'].mean(),
                    'avg_improvement': df['improvement'].mean(),
                    'improvement_rate': (df['improvement'] > 0).mean(),
                    'significant_improvement': (df['improvement'] > 0.1).sum()
                }
                
                logger.info(f"方法比较完成 - 改进率: {stats['improvement_rate']:.2%}, 平均改进: {stats['avg_improvement']:.3f}")
                return stats
            else:
                logger.warning("没有有效的比较结果")
                return {}
                
        except Exception as e:
            logger.error(f"比较情感分析方法失败: {e}")
            return {}
    
    def generate_report(self) -> str:
        """
        生成情感分析验证报告
        
        :return: 报告内容
        """
        try:
            logger.info("生成情感分析验证报告")
            
            # 执行各项测试
            accuracy_stats = self.validate_sentiment_accuracy()
            real_data_stats = self.test_real_data_sentiment()
            comparison_stats = self.compare_methods()
            
            # 生成报告
            report = f"""
# 情感分析验证报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 1. 准确性验证

- 测试用例数量: {accuracy_stats.get('total_cases', 0)}
- 准确案例数量: {accuracy_stats.get('accurate_cases', 0)}
- 准确率: {accuracy_stats.get('accuracy_rate', 0):.2%}
- 平均误差: {accuracy_stats.get('average_error', 0):.3f}
- 最大误差: {accuracy_stats.get('max_error', 0):.3f}
- 误差标准差: {accuracy_stats.get('std_error', 0):.3f}

## 2. 真实数据测试

- 样本数量: {real_data_stats.get('sample_size', 0)}
- 平均情感分数: {real_data_stats.get('mean_sentiment', 0):.3f}
- 情感分数标准差: {real_data_stats.get('std_sentiment', 0):.3f}
- 正面情感比例: {real_data_stats.get('positive_ratio', 0):.2%}
- 负面情感比例: {real_data_stats.get('negative_ratio', 0):.2%}
- 中性情感比例: {real_data_stats.get('neutral_ratio', 0):.2%}

## 3. 方法比较

- 原始方法平均误差: {comparison_stats.get('original_avg_error', 0):.3f}
- 改进方法平均误差: {comparison_stats.get('improved_avg_error', 0):.3f}
- 平均改进幅度: {comparison_stats.get('avg_improvement', 0):.3f}
- 改进案例比例: {comparison_stats.get('improvement_rate', 0):.2%}
- 显著改进案例: {comparison_stats.get('significant_improvement', 0)}

## 4. 建议

基于验证结果，建议：
1. 继续优化金融词典，增加更多专业术语
2. 调整权重配置，提高准确性
3. 增加更多测试用例，覆盖更多场景
4. 考虑引入机器学习模型进行进一步优化
"""
            
            return report
            
        except Exception as e:
            logger.error(f"生成验证报告失败: {e}")
            return "报告生成失败"


def main():
    """主函数"""
    try:
        validator = SentimentValidator()
        
        # 生成验证报告
        report = validator.generate_report()
        print(report)
        
        # 保存报告到文件
        report_file = f"sentiment_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"验证报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"情感分析验证失败: {e}")


if __name__ == "__main__":
    main()
