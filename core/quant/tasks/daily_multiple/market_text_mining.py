# -*- coding: UTF-8 -*-

"""
市场文本挖掘和情感分析系统 - 优化版本

0 */59 8-23 * * *

每日热词分析以及情绪评分
- 从多个数据源收集文本数据
- 进行情感分析和热词提取
- 更新数据库中的情感指标
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from contextlib import contextmanager

# 数据库相关
from db.database import db_manager
from db.models import Daliy, Jieba
from dao.quant_dao import DaliyDAO

# 工具类
from utils.jieba import JiebaAnalyse, cleanup_jieba_parallel
from utils.helper import clear_html, ago_day_timestr
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance, batch_process, batch_process_sequential, cleanup_all_executors
from config.settings import get_settings
from config.sentiment_config import sentiment_config, get_financial_keywords, classify_sentiment

# 机器学习相关
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import roc_auc_score, f1_score
from imblearn.over_sampling import SMOTE

# 其他依赖
from db.fetch import quant_monitor
from sqlalchemy.dialects.mysql import insert
from snownlp import SnowNLP
from snownlp import sentiment

class MarketTextMiner:
    """市场文本挖掘器"""

    def __init__(self):
        self.settings = get_settings()
        self.analyse = JiebaAnalyse()
        self.daliy_dao = DaliyDAO()

        # 初始化情感分析模型
        self._init_sentiment_model()

    def _init_sentiment_model(self):
        """初始化情感分析模型"""
        try:
            sentiment.classifier.load('finance_sentiment.marshal')
            logger.info("情感分析模型加载成功")
        except Exception as e:
            logger.warning(f"情感分析模型加载失败: {e}，使用默认模型")

    @contextmanager
    def get_db_session(self):
        """获取数据库会话"""
        with db_manager.get_session() as session:
            yield session

    @monitor_performance
    def analyze_sentiment(self, text: str) -> Tuple[Optional[float], Optional[float]]:
        """
        改进的文本情感分析

        使用多种方法结合：
        1. SnowNLP基础分析
        2. 金融词典匹配
        3. 上下文权重调整

        :param text: 输入文本
        :return: (积极概率, 消极概率)
        """
        try:
            if not text or not text.strip():
                return None, None

            # 清理HTML标签
            clean_text = clear_html(text)
            if not clean_text:
                return None, None

            # 方法1: SnowNLP基础分析
            s = SnowNLP(clean_text)
            base_positive_prob = s.sentiments

            # 方法2: 金融词典匹配分析
            dict_sentiment = self._analyze_sentiment_by_dictionary(clean_text)

            # 方法3: 上下文权重调整
            context_weight = self._calculate_context_weight(clean_text)

            # 综合计算最终情感分数
            final_positive_prob = self._combine_sentiment_scores(
                base_positive_prob, dict_sentiment, context_weight
            )

            # 确保分数在合理范围内
            final_positive_prob = max(0.0, min(1.0, final_positive_prob))
            negative_prob = 1 - final_positive_prob

            return final_positive_prob, negative_prob

        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return None, None

    def _analyze_sentiment_by_dictionary(self, text: str) -> float:
        """
        基于金融词典的情感分析

        :param text: 清理后的文本
        :return: 情感分数 (0-1)
        """
        try:
            # 加载正面和负面词汇
            positive_words = self._load_sentiment_words('positive.txt')
            negative_words = self._load_sentiment_words('negative.txt')

            # 计算正面和负面词汇出现次数
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)

            # 计算情感分数
            total_count = positive_count + negative_count
            if total_count == 0:
                return 0.5  # 中性

            sentiment_score = positive_count / total_count
            return sentiment_score

        except Exception as e:
            logger.error(f"词典情感分析失败: {e}")
            return 0.5

    def _load_sentiment_words(self, filename: str) -> set:
        """
        加载情感词汇文件

        :param filename: 文件名
        :return: 词汇集合
        """
        try:
            import os
            current_dir = os.path.dirname(__file__)
            utils_dir = os.path.join(current_dir, '..', '..', 'utils')
            file_path = os.path.join(utils_dir, filename)

            words = set()
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        # 提取词汇（去除标点符号）
                        import re
                        clean_words = re.findall(r'[\u4e00-\u9fa5]+', line.strip())
                        words.update(clean_words)

            return words

        except Exception as e:
            logger.error(f"加载情感词汇失败: {e}")
            return set()

    def _calculate_context_weight(self, text: str) -> float:
        """
        计算上下文权重

        考虑金融文本的特殊语境，使用配置文件中的关键词权重

        :param text: 文本内容
        :return: 权重系数 (0.5-1.5)
        """
        try:
            weight = 1.0

            # 使用配置文件中的金融关键词权重
            financial_keywords = get_financial_keywords()

            # 计算权重调整
            for keyword, factor in financial_keywords.items():
                if keyword in text:
                    # 使用乘法会导致权重过大，改为加权平均
                    weight = (weight + factor) / 2

            # 限制权重范围
            weight = max(0.5, min(1.5, weight))
            return weight

        except Exception as e:
            logger.error(f"计算上下文权重失败: {e}")
            return 1.0

    def _combine_sentiment_scores(self, base_score: float, dict_score: float,
                                context_weight: float) -> float:
        """
        综合多种情感分析结果

        使用配置文件中的权重设置

        :param base_score: SnowNLP基础分数
        :param dict_score: 词典匹配分数
        :param context_weight: 上下文权重
        :return: 最终情感分数
        """
        try:
            # 使用配置文件中的权重
            weights = sentiment_config.SENTIMENT_WEIGHTS
            base_weight = weights['base_weight']
            dict_weight = weights['dict_weight']
            context_factor = weights['context_factor']

            # 加权平均
            combined_score = (
                base_score * base_weight +
                dict_score * dict_weight
            )

            # 应用上下文权重调整
            if context_weight > 1.0:
                # 正向调整
                combined_score += (context_weight - 1.0) * context_factor
            else:
                # 负向调整
                combined_score -= (1.0 - context_weight) * context_factor

            return combined_score

        except Exception as e:
            logger.error(f"综合情感分数失败: {e}")
            return base_score

    @monitor_performance
    def batch_analyze_sentiment(self, texts: List[str]) -> List[Tuple[Optional[float], Optional[float]]]:
        """
        批量分析文本情感

        :param texts: 文本列表
        :return: 情感分析结果列表
        """
        def process_text(text):
            return self.analyze_sentiment(text)

        # 使用顺序处理避免多线程资源泄漏
        return batch_process_sequential(
            texts,
            process_text,
            batch_size=self.settings.quant.batch_size
        )

    @monitor_performance
    def fetch_media_data(self, days: int = 3) -> pd.DataFrame:
        """
        获取媒体数据

        :param days: 获取最近几天的数据
        :return: 媒体数据DataFrame
        """
        try:
            start_date = ago_day_timestr(days, '%Y-%m-%d')
            query = f'SELECT * FROM media WHERE createTime >= "{start_date}"'

            with db_manager.get_session() as session:
                df = pd.read_sql(query, con=session.bind)

            logger.info(f"获取媒体数据: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取媒体数据失败: {e}")
            return pd.DataFrame()

    @monitor_performance
    def fetch_news_data(self, days: int = 3) -> pd.DataFrame:
        """
        获取新闻数据

        :param days: 获取最近几天的数据
        :return: 新闻数据DataFrame
        """
        try:
            start_date = ago_day_timestr(days, '%Y-%m-%d %H:%M:%S')
            query = f'SELECT * FROM news WHERE time >= "{start_date}"'

            with db_manager.get_session() as session:
                df = pd.read_sql(query, con=session.bind)

            logger.info(f"获取新闻数据: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取新闻数据失败: {e}")
            return pd.DataFrame()

    @monitor_performance
    def fetch_research_data(self) -> pd.DataFrame:
        """
        获取研报数据

        :return: 研报数据DataFrame
        """
        try:
            query = 'SELECT * FROM research'

            with db_manager.get_session() as session:
                df = pd.read_sql(query, con=session.bind)

            logger.info(f"获取研报数据: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取研报数据失败: {e}")
            return pd.DataFrame()

    @monitor_performance
    def fetch_stock_trend_data(self, limit: int = 100, offset: int = 0) -> pd.DataFrame:
        """
        获取股票趋势数据

        :param limit: 限制条数
        :param offset: 偏移量
        :return: 股票趋势数据DataFrame
        """
        try:
            query = f'''
                SELECT * FROM stocktrend a
                LEFT JOIN stock b ON b.code = a.code
                ORDER BY id ASC
                LIMIT {limit} OFFSET {offset}
            '''

            with db_manager.get_session() as session:
                df = pd.read_sql(query, con=session.bind)

            logger.info(f"获取股票趋势数据: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取股票趋势数据失败: {e}")
            return pd.DataFrame()

    @monitor_performance
    def collect_all_texts(self) -> List[str]:
        """
        收集所有文本数据

        :return: 文本列表
        """
        words = []

        # 收集媒体数据
        df_media = self.fetch_media_data()
        if not df_media.empty and 'content' in df_media.columns:
            words.extend(df_media['content'].dropna().values.tolist())

        # 收集新闻数据
        df_news = self.fetch_news_data()
        if not df_news.empty and 'content' in df_news.columns:
            words.extend(df_news['content'].dropna().values.tolist())

        # 收集研报数据
        df_research = self.fetch_research_data()
        if not df_research.empty and 'title' in df_research.columns:
            words.extend(df_research['title'].dropna().values.tolist())

        # 收集股票趋势数据
        df_stock = self.fetch_stock_trend_data()
        if not df_stock.empty and 'remarks' in df_stock.columns:
            words.extend(df_stock['remarks'].dropna().values.tolist())

        # 过滤空值和清理文本
        clean_words = []
        for word in words:
            if word and isinstance(word, str) and word.strip():
                clean_word = clear_html(word.strip())
                if clean_word:
                    clean_words.append(clean_word)

        logger.info(f"收集文本数据: {len(clean_words)} 条")
        return clean_words

    @monitor_performance
    def update_hot_words(self, words: List[str], top_n: int = 100) -> List[str]:
        """
        更新热词排行

        :param words: 文本列表
        :param top_n: 取前N个热词
        :return: 热词列表
        """
        try:
            # 提取热词
            cut_words = self.analyse.cut_text_list(words, top_n)

            if not cut_words:
                logger.warning("没有提取到热词")
                return []

            # 更新数据库
            with self.get_db_session() as session:
                # 重置所有热词排名
                session.query(Jieba).update({"ranking": 0})
                session.commit()

                # 批量更新热词
                for index, word in enumerate(cut_words):
                    item = {
                        'text': word,
                        'ranking': top_n - index,
                    }

                    insert_stmt = insert(Jieba).values(**item)
                    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)

                    session.execute(on_duplicate_key_stmt)

                session.commit()

            logger.info(f"更新热词: {len(cut_words)} 个")
            return cut_words

        except Exception as e:
            logger.error(f"更新热词失败: {e}")
            return []

    @monitor_performance
    def analyze_market_sentiment(self) -> Optional[float]:
        """
        分析市场整体情感

        :return: 平均情感得分
        """
        try:
            # 获取媒体数据
            df_media = self.fetch_media_data()

            if df_media.empty or 'content' not in df_media.columns:
                logger.warning("没有媒体数据用于情感分析")
                return None

            # 批量情感分析
            contents = df_media['content'].dropna().values.tolist()
            if not contents:
                logger.warning("没有有效的内容用于情感分析")
                return None

            # 使用批量处理进行情感分析
            sentiment_results = self.batch_analyze_sentiment(contents)

            # 计算平均情感得分
            positive_scores = []
            for positive_prob, negative_prob in sentiment_results:
                if positive_prob is not None:
                    positive_scores.append(positive_prob)

            if not positive_scores:
                logger.warning("没有有效的情感分析结果")
                return None

            avg_sentiment = sum(positive_scores) / len(positive_scores)
            logger.info(f"市场情感分析完成，平均得分: {avg_sentiment:.4f}")

            return avg_sentiment

        except Exception as e:
            logger.error(f"市场情感分析失败: {e}")
            return None

    @monitor_performance
    def update_daily_sentiment(self, sentiment_score: float, hot_words: List[str]) -> bool:
        """
        更新每日情感数据

        :param sentiment_score: 情感得分
        :param hot_words: 热词列表
        :return: 是否成功
        """
        try:
            if sentiment_score <= 0:
                logger.warning("情感得分无效，跳过更新")
                return False

            # 生成情感描述（取前30个热词）
            sentiment_desc = ','.join(hot_words[:30]) if hot_words else ''

            # 获取今天的日期
            today = time.strftime('%Y%m%d', time.localtime())

            # 更新数据库
            with self.get_db_session() as session:
                new_daily = Daliy(
                    date=today,
                    sentiment=float(sentiment_score),
                    sentimentDesc=sentiment_desc
                )
                session.merge(new_daily)
                session.commit()

            logger.info(f"更新每日情感数据: 日期={today}, 得分={sentiment_score:.4f}")
            return True

        except Exception as e:
            logger.error(f"更新每日情感数据失败: {e}")
            return False

    @monitor_performance
    def run_analysis(self) -> bool:
        """
        运行完整的文本挖掘和情感分析流程

        :return: 是否成功
        """
        try:
            logger.info("开始市场文本挖掘和情感分析")

            # 1. 收集所有文本数据
            all_texts = self.collect_all_texts()
            if not all_texts:
                logger.warning("没有收集到文本数据")
                return False

            # 2. 分析市场情感
            sentiment_score = self.analyze_market_sentiment()
            if sentiment_score is None:
                logger.warning("情感分析失败")
                return False

            # 3. 更新热词排行
            hot_words = self.update_hot_words(all_texts)

            # 4. 更新每日情感数据
            success = self.update_daily_sentiment(sentiment_score, hot_words)

            # 5. 更新监控状态
            if success:
                quant_monitor(29, True)
                logger.info("市场文本挖掘和情感分析完成")

            return success

        except Exception as e:
            logger.error(f"市场文本挖掘和情感分析失败: {e}")
            quant_monitor(29, False)
            return False

def main():
    """主函数"""
    try:
        miner = MarketTextMiner()
        success = miner.run_analysis()

        if success:
            logger.info("✓ 市场文本挖掘任务执行成功")
        else:
            logger.error("✗ 市场文本挖掘任务执行失败")

        return success

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return False

if __name__ == "__main__":
    # 设置信号处理器
    setup_signal_handler()

    # 注册清理函数
    register_cleanup(cleanup_all_executors)
    register_cleanup(cleanup_jieba_parallel)

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)
