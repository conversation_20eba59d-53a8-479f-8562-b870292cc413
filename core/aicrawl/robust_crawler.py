#!/usr/bin/env python3
"""
更稳定的雪球爬虫 - 增加重试机制和更多选择器
"""
import asyncio
import json
import time
import random

async def robust_homepage_crawl(max_retries=3, delay_range=(5, 10)):
    """
    稳定的首页爬取，带重试机制
    """
    print("🔄 稳定版雪球首页爬取")
    print("=" * 50)
    
    from main_advanced import CrawlerApp
    
    app = CrawlerApp(enable_anti_detection=True)
    
    best_result = None
    best_score = 0
    
    for attempt in range(max_retries):
        print(f"\n📡 尝试 {attempt + 1}/{max_retries}")
        print("-" * 30)
        
        try:
            # 随机延迟避免被检测
            if attempt > 0:
                delay = random.uniform(*delay_range)
                print(f"⏰ 等待 {delay:.1f} 秒...")
                await asyncio.sleep(delay)
            
            # 执行爬取
            result = await app.crawl_homepage()
            
            if result.get('success'):
                hot_stocks_count = result.get('hot_stocks_count', 0)
                hot_topics_count = result.get('hot_topics_count', 0)
                
                # 计算结果质量分数
                score = hot_stocks_count + hot_topics_count
                
                print(f"✅ 爬取成功:")
                print(f"   热门股票: {hot_stocks_count} 个")
                print(f"   热门话题: {hot_topics_count} 个")
                print(f"   质量分数: {score}")
                
                # 保存最佳结果
                if score > best_score:
                    best_result = result
                    best_score = score
                    print(f"🏆 当前最佳结果 (分数: {score})")
                
                # 如果获得了完整数据，提前结束
                if hot_stocks_count > 0 and hot_topics_count > 0:
                    print(f"🎯 获得完整数据，提前结束")
                    break
                    
            else:
                print(f"❌ 爬取失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    # 返回最佳结果
    if best_result:
        print(f"\n🏆 最终结果 (最佳分数: {best_score})")
        print("-" * 30)
        
        # 格式化输出
        hot_stocks = best_result.get('hot_stocks', [])
        hot_topics = best_result.get('hot_topics', [])
        
        if hot_stocks:
            print(f"📈 热门股票 ({len(hot_stocks)} 个):")
            for i, stock in enumerate(hot_stocks[:5]):
                print(f"  {i+1}. {stock['symbol']} - {stock['name']}: {stock['change_percent']}%")
        else:
            print("📈 热门股票: 暂无数据")
        
        if hot_topics:
            print(f"\n🔥 热门话题 ({len(hot_topics)} 个):")
            for i, topic in enumerate(hot_topics[:5]):
                print(f"  {i+1}. {topic['title']}")
        else:
            print("\n🔥 热门话题: 暂无数据")
        
        # 保存结果
        timestamp = int(time.time())
        filename = f"robust_result_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(best_result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {filename}")
        
        return best_result
    else:
        print(f"\n❌ 所有尝试都失败了")
        return None

async def test_different_approaches():
    """
    测试不同的爬取方法
    """
    print("🧪 测试不同爬取方法")
    print("=" * 50)
    
    from crawler_engine import XueqiuCrawler
    from extractors import XueqiuExtractor
    
    crawler = XueqiuCrawler(enable_anti_detection=True)
    extractor = XueqiuExtractor()
    
    approaches = [
        ("标准方法", {}),
        ("等待更长时间", {"wait_for": "networkidle", "page_load_timeout": 30}),
        ("禁用图片", {"exclude_external_images": True}),
        ("简单模式", {"exclude_external_links": True, "exclude_external_images": True})
    ]
    
    results = []
    
    for name, kwargs in approaches:
        print(f"\n🔬 测试: {name}")
        print("-" * 30)
        
        try:
            result = await crawler.crawl_homepage(**kwargs)
            
            if result.success:
                # 提取数据
                homepage_data = extractor.extract_homepage_data(result.raw_html)
                
                if homepage_data:
                    hot_stocks_count = len(homepage_data.hot_stocks)
                    hot_topics_count = len(homepage_data.hot_topics)
                    
                    print(f"✅ {name}: 股票 {hot_stocks_count} 个, 话题 {hot_topics_count} 个")
                    
                    results.append({
                        "method": name,
                        "hot_stocks_count": hot_stocks_count,
                        "hot_topics_count": hot_topics_count,
                        "total_score": hot_stocks_count + hot_topics_count
                    })
                else:
                    print(f"❌ {name}: 数据提取失败")
            else:
                print(f"❌ {name}: 页面访问失败 - {result.error}")
                
        except Exception as e:
            print(f"❌ {name}: 异常 - {e}")
        
        # 等待避免请求过快
        await asyncio.sleep(3)
    
    # 显示结果对比
    if results:
        print(f"\n📊 方法对比")
        print("-" * 30)
        results.sort(key=lambda x: x['total_score'], reverse=True)
        
        for i, result in enumerate(results):
            print(f"{i+1}. {result['method']}: 总分 {result['total_score']} (股票 {result['hot_stocks_count']}, 话题 {result['hot_topics_count']})")
        
        best_method = results[0]
        print(f"\n🏆 最佳方法: {best_method['method']} (总分: {best_method['total_score']})")
    
    return results

async def main():
    """主函数"""
    print("🚀 雪球爬虫稳定性测试")
    print("=" * 60)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 稳定爬取 (多次重试)")
    print("2. 方法对比 (测试不同参数)")
    print("3. 两者都执行")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice in ['1', '3']:
        print("\n" + "="*60)
        await robust_homepage_crawl()
    
    if choice in ['2', '3']:
        print("\n" + "="*60)
        await test_different_approaches()
    
    print(f"\n✅ 测试完成")
    print("\n💡 使用建议:")
    print("1. 如果数据不稳定，可以多次运行取最佳结果")
    print("2. 适当增加请求间隔 (5-10秒)")
    print("3. 避免在短时间内频繁访问")
    print("4. 考虑在不同时间段进行爬取")

if __name__ == "__main__":
    asyncio.run(main())
