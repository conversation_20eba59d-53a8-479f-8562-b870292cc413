#!/usr/bin/env python3
"""
测试基于HTML解析的雪球爬虫功能
专注于页面HTML解析，避免API调用
"""
import asyncio
import logging
import json
from pathlib import Path

from main_advanced import CrawlerApp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_single_stock_html():
    """测试单个股票HTML页面解析"""
    print("=" * 60)
    print("测试单个股票HTML页面解析")
    print("=" * 60)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"  # 测试股票代码
    
    try:
        print(f"正在爬取股票: {test_symbol}")
        print("URL:", f"https://xueqiu.com/S/{test_symbol}")
        
        result = await app.crawl_single_stock(test_symbol)
        
        if result.get('success'):
            print("✓ 股票基本信息爬取成功")
            print(f"股票名称: {result.get('name', 'N/A')}")
            print(f"当前价格: ¥{result.get('current_price', 'N/A')}")
            print(f"涨跌幅: {result.get('change_percent', 'N/A')}%")
            print(f"成交量: {result.get('volume', 'N/A')}")
            print(f"市值: {result.get('market_cap', 'N/A')}")
            
            # 保存结果
            output_file = f"html_stock_test_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 股票信息爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 爬取异常: {e}")
        return False

async def test_homepage_html():
    """测试首页HTML解析"""
    print("=" * 60)
    print("测试雪球首页HTML解析")
    print("=" * 60)
    
    app = CrawlerApp(enable_anti_detection=True)
    
    try:
        print("正在爬取雪球首页...")
        print("URL: https://xueqiu.com")
        
        result = await app.crawl_homepage()
        
        if result.get('success'):
            print("✓ 首页数据爬取成功")
            print(f"热门股票数量: {result.get('hot_stocks_count', 0)}")
            print(f"热门话题数量: {result.get('hot_topics_count', 0)}")
            
            # 显示部分热门股票
            hot_stocks = result.get('hot_stocks', [])
            if hot_stocks:
                print("\n前5个热门股票:")
                for i, stock in enumerate(hot_stocks[:5], 1):
                    print(f"  {i}. {stock.get('symbol', 'N/A')} - {stock.get('name', 'N/A')}")
                    print(f"     价格: ¥{stock.get('current_price', 'N/A')}, 涨跌: {stock.get('change_percent', 'N/A')}%")
            
            # 显示部分热门话题
            hot_topics = result.get('hot_topics', [])
            if hot_topics:
                print("\n前3个热门话题:")
                for i, topic in enumerate(hot_topics[:3], 1):
                    title = topic.get('title', 'N/A')
                    if len(title) > 50:
                        title = title[:50] + "..."
                    print(f"  {i}. {title}")
                    print(f"     作者: {topic.get('author', 'N/A')}, 浏览: {topic.get('view_count', 'N/A')}")
            
            # 保存结果
            output_file = "html_homepage_test.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 首页爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 首页爬取异常: {e}")
        return False

async def test_stock_detail_html():
    """测试个股详细页面HTML解析"""
    print("=" * 60)
    print("测试个股详细页面HTML解析")
    print("=" * 60)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"
    
    try:
        print(f"正在爬取股票详细信息: {test_symbol}")
        
        result = await app.crawl_stock_detail(test_symbol)
        
        if result.get('success'):
            print("✓ 个股详细信息爬取成功")
            
            # 显示基本信息
            basic_info = result.get('basic_info')
            if basic_info and basic_info.get('success'):
                print(f"股票名称: {basic_info.get('name', 'N/A')}")
                print(f"当前价格: ¥{basic_info.get('current_price', 'N/A')}")
            
            print(f"评论数量: {result.get('comments_count', 0)}")
            print(f"新闻数量: {result.get('news_count', 0)}")
            
            # 保存结果
            output_file = f"html_detail_test_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 详细信息爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 详细信息爬取异常: {e}")
        return False

async def test_comments_html():
    """测试评论HTML解析"""
    print("=" * 60)
    print("测试评论HTML解析")
    print("=" * 60)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"
    
    try:
        print(f"正在从股票页面提取评论: {test_symbol}")
        print("注意: 评论数据从股票页面HTML中提取，不使用API")
        
        result = await app.crawl_stock_comments(test_symbol, count=5)
        
        if result.get('success'):
            print("✓ 评论数据爬取成功")
            print(f"总评论数: {result.get('total_comments', 0)}")
            
            comments = result.get('comments', [])
            if comments:
                print("\n评论预览:")
                for i, comment in enumerate(comments[:3], 1):
                    content = comment.get('content', '')
                    if len(content) > 100:
                        content = content[:100] + "..."
                    print(f"  {i}. {comment.get('author', 'N/A')}: {content}")
                    print(f"     时间: {comment.get('publish_time', 'N/A')}, 点赞: {comment.get('like_count', 'N/A')}")
            else:
                print("未找到评论数据（可能需要登录或页面结构变化）")
            
            # 保存结果
            output_file = f"html_comments_test_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 评论爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 评论爬取异常: {e}")
        return False

async def run_html_tests():
    """运行所有HTML解析测试"""
    print("开始运行基于HTML解析的雪球爬虫测试")
    print("测试策略: 专注HTML页面解析，避免API调用")
    print("测试股票: SH688775")
    print()
    
    test_results = []
    
    # 测试1: 单个股票基本信息
    print("测试1/4: 单个股票基本信息")
    result1 = await test_single_stock_html()
    test_results.append(("股票基本信息", result1))
    
    # 等待避免请求过快
    await asyncio.sleep(5)
    
    # 测试2: 首页解析
    print("\n测试2/4: 首页解析")
    result2 = await test_homepage_html()
    test_results.append(("首页解析", result2))
    
    await asyncio.sleep(5)
    
    # 测试3: 个股详细信息
    print("\n测试3/4: 个股详细信息")
    result3 = await test_stock_detail_html()
    test_results.append(("个股详细信息", result3))
    
    await asyncio.sleep(5)
    
    # 测试4: 评论解析
    print("\n测试4/4: 评论解析")
    result4 = await test_comments_html()
    test_results.append(("评论解析", result4))
    
    # 打印测试总结
    print("\n" + "=" * 60)
    print("HTML解析测试总结")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(test_results)} 测试通过")
    
    if success_count >= len(test_results) // 2:
        print("🎉 大部分测试通过！HTML解析策略有效！")
        print("\n💡 使用建议:")
        print("- 基于HTML解析比API调用更稳定")
        print("- 适当增加请求间隔避免被封IP")
        print("- 可以根据实际页面结构调整选择器")
    else:
        print("⚠️  多数测试失败，可能需要:")
        print("- 检查网络连接")
        print("- 更新HTML选择器")
        print("- 调整反爬虫策略")

if __name__ == "__main__":
    asyncio.run(run_html_tests())
