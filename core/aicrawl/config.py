"""
配置文件 - 反爬虫系统配置
"""
import random
from typing import List, Dict, Any

class AntiDetectionConfig:
    """反检测配置类"""
    
    # 用户代理池 - 模拟真实浏览器
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    
    # 请求头模板
    HEADERS_TEMPLATES = [
        {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        },
        {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
    ]
    
    # 代理配置 (需要用户自己配置)
    PROXY_POOLS = [
        # 示例格式，用户需要替换为真实代理
        # {"http": "http://proxy1:port", "https": "https://proxy1:port"},
        # {"http": "http://proxy2:port", "https": "https://proxy2:port"},
    ]
    
    # 请求频率控制
    REQUEST_DELAYS = {
        "min_delay": 2,      # 最小延迟(秒)
        "max_delay": 8,      # 最大延迟(秒)
        "burst_delay": 15,   # 突发请求后的延迟(秒)
        "error_delay": 30    # 错误后的延迟(秒)
    }
    
    # 重试配置
    RETRY_CONFIG = {
        "max_retries": 3,
        "retry_delay": 5,
        "backoff_factor": 2
    }
    
    # 浏览器配置
    BROWSER_CONFIG = {
        "headless": True,
        "disable_images": True,
        "disable_css": False,
        "disable_javascript": False,
        "viewport": {"width": 1920, "height": 1080},
        "timeout": 30000
    }
    
    # 雪球网站特定配置
    XUEQIU_CONFIG = {
        "base_url": "https://xueqiu.com",
        "homepage_url": "https://xueqiu.com",
        "stock_url_pattern": "https://xueqiu.com/S/{symbol}",
        "required_cookies": [
            # 雪球可能需要的cookie，通过浏览器访问获取
        ],
        # 专注于HTML页面爬取，避免API反爬
        "crawl_settings": {
            "wait_for_load": True,
            "enable_javascript": True,
            "page_load_timeout": 30,
            "scroll_to_load_comments": True,
            "delay_between_requests": 3
        },
        "url_patterns": {
            "homepage": "https://xueqiu.com",
            "stock_detail": "https://xueqiu.com/S/{symbol}",
            "stock_comments": "https://xueqiu.com/S/{symbol}#comments",
            "user_profile": "https://xueqiu.com/u/{user_id}",
            "topic": "https://xueqiu.com/topic/{topic_id}"
        }
    }
    
    @classmethod
    def get_random_user_agent(cls) -> str:
        """获取随机User-Agent"""
        return random.choice(cls.USER_AGENTS)
    
    @classmethod
    def get_random_headers(cls) -> Dict[str, str]:
        """获取随机请求头"""
        headers = random.choice(cls.HEADERS_TEMPLATES).copy()
        headers["User-Agent"] = cls.get_random_user_agent()
        return headers
    
    @classmethod
    def get_random_delay(cls) -> float:
        """获取随机延迟时间"""
        return random.uniform(
            cls.REQUEST_DELAYS["min_delay"],
            cls.REQUEST_DELAYS["max_delay"]
        )
    
    @classmethod
    def get_random_proxy(cls) -> Dict[str, str]:
        """获取随机代理"""
        if not cls.PROXY_POOLS:
            return None
        return random.choice(cls.PROXY_POOLS)

# 数据提取配置
class ExtractionConfig:
    """数据提取配置"""
    
    # 雪球网站HTML选择器配置 - 基于实际页面结构
    XUEQIU_SELECTORS = {
        # 个股页面基本信息选择器
        "stock_info": {
            "name": [
                "h1.stock-name",
                ".stock-info h1",
                ".quote-info .name",
                "[data-reactid*='name']",
                "h1"
            ],
            "code": [
                ".stock-symbol",
                ".quote-info .symbol",
                "[data-reactid*='symbol']",
                ".stock-code"
            ],
            "price": [
                ".stock-current",
                ".current-price",
                ".quote-price",
                "[data-reactid*='current']",
                ".price-current",
                ".stock-price .current"
            ],
            "change": [
                ".stock-change",
                ".change-amount",
                "[data-reactid*='change']",
                ".price-change .amount"
            ],
            "change_percent": [
                ".stock-percent",
                ".change-percent",
                "[data-reactid*='percent']",
                ".price-change .percent"
            ],
            "volume": [
                ".stock-volume",
                ".volume",
                "[data-reactid*='volume']",
                ".trade-volume"
            ],
            "market_cap": [
                ".stock-market-cap",
                ".market-cap",
                "[data-reactid*='market_capital']",
                ".market-value"
            ]
        },

        # 首页热门股票选择器
        "homepage_hot_stocks": {
            "container": [
                ".hot-stocks",
                ".rank-list",
                ".stock-list",
                ".popular-stocks",
                "[data-reactid*='hot']"
            ],
            "item": [
                ".stock-item",
                ".rank-item",
                ".list-item",
                "li",
                ".stock-row"
            ],
            "symbol": [
                ".symbol",
                ".code",
                "[data-symbol]",
                ".stock-code"
            ],
            "name": [
                ".name",
                ".stock-name",
                ".title"
            ],
            "price": [
                ".price",
                ".current",
                ".current-price"
            ],
            "change_percent": [
                ".percent",
                ".change-percent",
                ".change"
            ]
        },

        # 首页热门话题选择器
        "homepage_hot_topics": {
            "container": [
                ".hot-topics",
                ".topic-list",
                ".article-list",
                ".news-list"
            ],
            "item": [
                ".topic-item",
                ".article-item",
                ".news-item",
                "li",
                ".topic-row"
            ],
            "title": [
                ".title",
                ".topic-title",
                "h3",
                "a"
            ],
            "author": [
                ".author",
                ".user-name",
                ".username"
            ],
            "view_count": [
                ".view-count",
                ".views",
                ".read-count"
            ],
            "comment_count": [
                ".comment-count",
                ".comments",
                ".reply-count"
            ]
        },

        # 评论区选择器
        "comments": {
            "container": [
                ".comments-section",
                ".comment-list",
                ".timeline",
                ".status-list"
            ],
            "item": [
                ".comment-item",
                ".status-item",
                ".timeline-item",
                "li"
            ],
            "content": [
                ".comment-content",
                ".status-content",
                ".text",
                ".content"
            ],
            "author": [
                ".comment-author",
                ".user-name",
                ".author",
                ".username"
            ],
            "time": [
                ".comment-time",
                ".publish-time",
                ".time",
                ".created-at"
            ],
            "like_count": [
                ".like-count",
                ".fav-count",
                ".likes"
            ]
        }
    }
    
    # JSON数据提取规则
    JSON_EXTRACTION_RULES = {
        "quote_data": {
            "path": "window.SNB.data.quote",
            "fields": {
                "symbol": "symbol",
                "name": "name", 
                "current": "current",
                "percent": "percent",
                "chg": "chg",
                "volume": "volume",
                "amount": "amount",
                "market_capital": "market_capital",
                "pe_ttm": "pe_ttm",
                "pb": "pb"
            }
        }
    }
