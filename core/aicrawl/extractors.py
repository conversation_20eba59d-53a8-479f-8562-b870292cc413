"""
数据提取器 - 专门针对雪球网站的数据提取策略
"""
import re
import json
import logging
from typing import Dict, List, Optional, Any, Union
from bs4 import BeautifulSoup
from dataclasses import dataclass

from config import ExtractionConfig

logger = logging.getLogger(__name__)

@dataclass
class StockData:
    """股票数据结构"""
    symbol: str
    name: str
    current_price: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    amount: Optional[float] = None
    market_cap: Optional[float] = None
    pe_ttm: Optional[float] = None
    pb: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open_price: Optional[float] = None
    prev_close: Optional[float] = None
    high_52w: Optional[float] = None
    low_52w: Optional[float] = None
    eps: Optional[float] = None
    dividend_yield: Optional[float] = None
    raw_data: Optional[Dict] = None

@dataclass
class HotTopicData:
    """热门话题数据结构"""
    title: str = ""
    url: str = ""
    view_count: Optional[int] = None
    comment_count: Optional[int] = None
    like_count: Optional[int] = None
    author: str = ""
    publish_time: Optional[str] = None
    summary: str = ""
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class HotStockData:
    """热门股票数据结构"""
    symbol: str = ""
    name: str = ""
    current_price: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    reason: str = ""  # 上榜原因
    rank: Optional[int] = None
    heat_score: Optional[float] = None

@dataclass
class CommentData:
    """评论数据结构"""
    comment_id: str = ""
    content: str = ""
    author: str = ""
    author_id: str = ""
    publish_time: Optional[str] = None
    like_count: Optional[int] = None
    reply_count: Optional[int] = None
    parent_id: Optional[str] = None  # 父评论ID，用于回复
    sentiment: Optional[str] = None  # 情感分析结果：positive/negative/neutral

@dataclass
class XueqiuHomePageData:
    """雪球首页数据结构"""
    hot_stocks: List[HotStockData] = None
    hot_topics: List[HotTopicData] = None
    market_summary: Dict[str, Any] = None
    crawl_time: Optional[str] = None

    def __post_init__(self):
        if self.hot_stocks is None:
            self.hot_stocks = []
        if self.hot_topics is None:
            self.hot_topics = []
        if self.market_summary is None:
            self.market_summary = {}

@dataclass
class StockDetailData:
    """个股详细数据结构"""
    basic_info: StockData = None
    comments: List[CommentData] = None
    news: List[HotTopicData] = None
    financial_data: Dict[str, Any] = None
    technical_indicators: Dict[str, Any] = None

    def __post_init__(self):
        if self.comments is None:
            self.comments = []
        if self.news is None:
            self.news = []
        if self.financial_data is None:
            self.financial_data = {}
        if self.technical_indicators is None:
            self.technical_indicators = {}

class XueqiuExtractor:
    """雪球网站数据提取器"""
    
    def __init__(self):
        self.selectors = ExtractionConfig.XUEQIU_SELECTORS
        self.json_rules = ExtractionConfig.JSON_EXTRACTION_RULES
        
    def extract_from_html(self, html_content: str, url: str = None) -> Optional[StockData]:
        """从HTML内容提取股票数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 首先尝试从JavaScript变量中提取数据
            js_data = self._extract_from_javascript(html_content)
            if js_data:
                return js_data
                
            # 如果JS提取失败，尝试从HTML元素提取
            return self._extract_from_dom(soup, url)
            
        except Exception as e:
            logger.error(f"HTML提取失败: {e}")
            return None
    
    def _extract_from_javascript(self, html_content: str) -> Optional[StockData]:
        """从JavaScript变量中提取数据"""
        try:
            # 查找SNB数据的多种模式
            patterns = [
                r'SNB\s*=\s*\{[^{]*data:\s*\{[^{]*quote:\s*(\{[^}]+\})',
                r'window\.SNB\s*=\s*\{.*?data:\s*\{.*?quote:\s*(\{.*?\})',
                r'SNB\s*=\s*\{.*?quote:\s*(\{.*?\})',
                r'data:\s*\{.*?quote:\s*(\{[^}]*\})'
            ]

            for pattern in patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        quote_str = match.group(1)
                        # 清理和修复JSON字符串
                        quote_str = self._clean_json_string(quote_str)
                        quote_data = json.loads(quote_str)
                        result = self._parse_quote_data(quote_data)
                        if result:
                            return result
                    except Exception as e:
                        logger.debug(f"解析模式失败: {pattern[:50]}... - {e}")
                        continue

            # 尝试其他可能的数据源
            return self._extract_from_other_js_sources(html_content)

        except Exception as e:
            logger.error(f"JavaScript数据提取失败: {e}")
            return None
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串"""
        # 移除注释
        json_str = re.sub(r'//.*?\n', '', json_str)
        # 移除多余的逗号
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        return json_str
    
    def _extract_from_other_js_sources(self, html_content: str) -> Optional[StockData]:
        """从其他JavaScript数据源提取"""
        patterns = [
            r'window\.STOCK_PAGE\s*=\s*true;.*?SNB\s*=\s*\{.*?data:\s*\{.*?quote:\s*(\{.*?\})',
            r'var\s+stockData\s*=\s*(\{.*?\});',
            r'window\.stockInfo\s*=\s*(\{.*?\});'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content, re.DOTALL)
            if match:
                try:
                    data_str = match.group(1)
                    data_str = self._clean_json_string(data_str)
                    data = json.loads(data_str)
                    return self._parse_quote_data(data)
                except:
                    continue
                    
        return None
    
    def _parse_quote_data(self, quote_data: Dict) -> StockData:
        """解析quote数据为StockData对象"""
        try:
            return StockData(
                symbol=quote_data.get('symbol', ''),
                name=quote_data.get('name', ''),
                current_price=self._safe_float(quote_data.get('current')),
                change=self._safe_float(quote_data.get('chg')),
                change_percent=self._safe_float(quote_data.get('percent')),
                volume=self._safe_int(quote_data.get('volume')),
                amount=self._safe_float(quote_data.get('amount')),
                market_cap=self._safe_float(quote_data.get('market_capital')),
                pe_ttm=self._safe_float(quote_data.get('pe_ttm')),
                pb=self._safe_float(quote_data.get('pb')),
                high=self._safe_float(quote_data.get('high')),
                low=self._safe_float(quote_data.get('low')),
                open_price=self._safe_float(quote_data.get('open')),
                prev_close=self._safe_float(quote_data.get('last_close')),
                high_52w=self._safe_float(quote_data.get('high52w')),
                low_52w=self._safe_float(quote_data.get('low52w')),
                eps=self._safe_float(quote_data.get('eps')),
                dividend_yield=self._safe_float(quote_data.get('dividend_yield')),
                raw_data=quote_data
            )
        except Exception as e:
            logger.error(f"解析quote数据失败: {e}")
            return None
    
    def _extract_from_dom(self, soup: BeautifulSoup, url: str = None) -> Optional[StockData]:
        """从DOM元素提取数据"""
        try:
            # 从URL提取股票代码
            symbol = self._extract_symbol_from_url(url) if url else ''

            # 尝试多种选择器提取基本信息
            name_selectors = ['h1', '.stock-name', 'title', '[data-symbol]']
            price_selectors = ['.stock-current', '.current-price', '.price', '[data-current]']

            name = None
            for selector in name_selectors:
                name = self._extract_text(soup, selector)
                if name and symbol in name:
                    break

            current_price = None
            for selector in price_selectors:
                current_price = self._extract_number(soup, selector)
                if current_price:
                    break

            # 如果没有找到价格，尝试从文本中提取数字
            if not current_price and name:
                price_match = re.search(r'[\d,]+\.?\d*', name)
                if price_match:
                    try:
                        current_price = float(price_match.group().replace(',', ''))
                    except:
                        pass

            change = self._extract_number(soup, '.stock-change', '.change')
            change_percent = self._extract_number(soup, '.stock-percent', '.percent')

            # 如果有基本信息就返回
            if symbol or name or current_price:
                return StockData(
                    symbol=symbol,
                    name=name or '',
                    current_price=current_price,
                    change=change,
                    change_percent=change_percent
                )

            return None

        except Exception as e:
            logger.error(f"DOM提取失败: {e}")
            return None
    
    def _extract_symbol_from_url(self, url: str) -> str:
        """从URL提取股票代码"""
        match = re.search(r'/S/([A-Z0-9]+)', url)
        return match.group(1) if match else ''
    
    def _extract_text(self, soup: BeautifulSoup, *selectors) -> Optional[str]:
        """提取文本内容"""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_number(self, soup: BeautifulSoup, *selectors) -> Optional[float]:
        """提取数字"""
        text = self._extract_text(soup, *selectors)
        if text:
            # 清理数字字符串
            number_str = re.sub(r'[^\d.-]', '', text)
            try:
                return float(number_str)
            except:
                pass
        return None
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """安全转换为float"""
        if value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为int"""
        if value is None:
            return None
        try:
            return int(float(value))
        except:
            return None

    def _extract_text_by_selectors(self, element, selectors: List[str]) -> str:
        """使用多个选择器尝试提取文本"""
        for selector in selectors:
            try:
                text = self._extract_text(element, selector)
                if text and text.strip():
                    return text.strip()
            except:
                continue
        return ""

    def _extract_number_by_selectors(self, element, selectors: List[str]) -> Optional[float]:
        """使用多个选择器尝试提取数字"""
        for selector in selectors:
            try:
                number = self._extract_number(element, selector)
                if number is not None:
                    return number
            except:
                continue
        return None

    def _extract_attribute_by_selectors(self, element, selectors: List[str], attr: str) -> str:
        """使用多个选择器尝试提取属性"""
        for selector in selectors:
            try:
                elem = element.select_one(selector)
                if elem and elem.get(attr):
                    return elem.get(attr).strip()
            except:
                continue
        return ""

    def extract_homepage_data(self, html_content: str) -> Optional[XueqiuHomePageData]:
        """提取雪球首页数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取热门股票
            hot_stocks = self._extract_hot_stocks(soup, html_content)

            # 提取热门话题
            hot_topics = self._extract_hot_topics(soup, html_content)

            # 提取市场概况
            market_summary = self._extract_market_summary(soup, html_content)

            return XueqiuHomePageData(
                hot_stocks=hot_stocks,
                hot_topics=hot_topics,
                market_summary=market_summary,
                crawl_time=self._get_current_time()
            )

        except Exception as e:
            logger.error(f"首页数据提取失败: {e}")
            return None

    def extract_stock_comments(self, html_content: str, symbol: str = None) -> List[CommentData]:
        """提取个股评论数据"""
        try:
            # 尝试从JSON API响应中提取
            if self._is_json_response(html_content):
                return self._extract_comments_from_json(html_content)

            # 从HTML页面提取
            soup = BeautifulSoup(html_content, 'html.parser')
            return self._extract_comments_from_html(soup)

        except Exception as e:
            logger.error(f"评论数据提取失败: {e}")
            return []

    def extract_stock_detail(self, html_content: str, url: str = None) -> Optional[StockDetailData]:
        """提取个股详细数据"""
        try:
            # 提取基本股票信息
            basic_info = self.extract_from_html(html_content, url)

            # 提取评论（如果页面包含）
            comments = self.extract_stock_comments(html_content)

            # 提取相关新闻
            news = self._extract_stock_news(html_content)

            # 提取财务数据
            financial_data = self._extract_financial_data(html_content)

            # 提取技术指标
            technical_indicators = self._extract_technical_indicators(html_content)

            return StockDetailData(
                basic_info=basic_info,
                comments=comments,
                news=news,
                financial_data=financial_data,
                technical_indicators=technical_indicators
            )

        except Exception as e:
            logger.error(f"个股详细数据提取失败: {e}")
            return None

    def _extract_hot_stocks(self, soup: BeautifulSoup, html_content: str) -> List[HotStockData]:
        """提取热门股票 - 专注HTML解析"""
        hot_stocks = []
        try:
            selectors = self.selectors.get("homepage_hot_stocks", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到热门股票容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到热门股票容器，在整个页面中查找")

            # 查找股票项目
            stock_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    stock_elements = elements
                    logger.debug(f"找到 {len(elements)} 个股票项目: {item_selector}")
                    break

            # 提取每个股票的信息
            for i, element in enumerate(stock_elements[:10]):  # 限制前10个
                try:
                    # 提取股票代码 - 优先从href中提取
                    symbol = ""
                    for selector in selectors.get("symbol", []):
                        elem = element.select_one(selector)
                        if elem:
                            if selector == "a[href*='/S/']" and elem.get('href'):
                                href = elem.get('href', '')
                                if '/S/' in href:
                                    symbol = href.split('/S/')[-1]
                                    break
                            else:
                                symbol = elem.get_text(strip=True)
                                if symbol:
                                    break

                    # 提取股票名称
                    name = self._extract_text_by_selectors(element, selectors.get("name", []))

                    # 提取价格
                    price = self._extract_number_by_selectors(element, selectors.get("price", []))

                    # 提取涨跌幅
                    change_percent = self._extract_number_by_selectors(element, selectors.get("change_percent", []))

                    # 如果至少有股票代码或名称，就创建记录
                    if symbol or name:
                        hot_stock = HotStockData(
                            symbol=symbol or '',
                            name=name or '',
                            current_price=price,
                            change_percent=change_percent,
                            rank=i + 1
                        )
                        hot_stocks.append(hot_stock)
                        logger.debug(f"提取热门股票: {symbol} - {name}")

                except Exception as e:
                    logger.debug(f"提取单个热门股票失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"热门股票提取失败: {e}")

        logger.info(f"成功提取 {len(hot_stocks)} 个热门股票")
        return hot_stocks

    def _extract_hot_topics(self, soup: BeautifulSoup, html_content: str) -> List[HotTopicData]:
        """提取热门话题 - 专注HTML解析"""
        hot_topics = []
        try:
            selectors = self.selectors.get("homepage_hot_topics", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到热门话题容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到热门话题容器，在整个页面中查找")

            # 查找话题项目
            topic_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    topic_elements = elements
                    logger.debug(f"找到 {len(elements)} 个话题项目: {item_selector}")
                    break

            # 提取每个话题的信息
            for element in topic_elements[:10]:  # 限制前10个
                try:
                    # 提取标题和链接
                    title = self._extract_text_by_selectors(element, selectors.get("title", []))
                    url = self._extract_attribute_by_selectors(element, selectors.get("title", []), "href")

                    # 提取作者
                    author = self._extract_text_by_selectors(element, selectors.get("author", []))

                    # 提取浏览量
                    view_count = self._extract_number_by_selectors(element, selectors.get("view_count", []))

                    # 提取评论数
                    comment_count = self._extract_number_by_selectors(element, selectors.get("comment_count", []))

                    # 如果至少有标题，就创建记录
                    if title:
                        # 处理相对URL
                        if url and url.startswith('/'):
                            url = 'https://xueqiu.com' + url

                        hot_topic = HotTopicData(
                            title=title,
                            url=url or '',
                            view_count=self._safe_int(view_count),
                            comment_count=self._safe_int(comment_count),
                            author=author or ''
                        )
                        hot_topics.append(hot_topic)
                        logger.debug(f"提取热门话题: {title}")

                except Exception as e:
                    logger.debug(f"提取单个热门话题失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"热门话题提取失败: {e}")

        logger.info(f"成功提取 {len(hot_topics)} 个热门话题")
        return hot_topics

    def _extract_market_summary(self, soup: BeautifulSoup, html_content: str) -> Dict[str, Any]:
        """提取市场概况"""
        market_summary = {}
        try:
            # 尝试从JavaScript数据中提取市场指数
            js_pattern = r'marketData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    market_data = json.loads(match.group(1))
                    market_summary.update(market_data)
                except:
                    pass

            # 从HTML元素提取市场指数
            index_elements = soup.select('.market-index, .index-item')
            for element in index_elements:
                try:
                    name = self._extract_text(element, '.index-name, .name')
                    value = self._extract_number(element, '.index-value, .value')
                    change = self._extract_number(element, '.index-change, .change')
                    if name and value:
                        market_summary[name] = {
                            'value': value,
                            'change': change
                        }
                except:
                    continue

        except Exception as e:
            logger.error(f"市场概况提取失败: {e}")

        return market_summary

    def _extract_comments_from_json(self, json_content: str) -> List[CommentData]:
        """从JSON响应中提取评论"""
        comments = []
        try:
            data = json.loads(json_content)

            # 雪球API的评论数据结构
            if 'list' in data:
                comment_list = data['list']
            elif 'data' in data and isinstance(data['data'], list):
                comment_list = data['data']
            else:
                comment_list = []

            for comment_data in comment_list:
                try:
                    comment = CommentData(
                        comment_id=str(comment_data.get('id', '')),
                        content=comment_data.get('text', ''),
                        author=comment_data.get('user', {}).get('screen_name', ''),
                        author_id=str(comment_data.get('user', {}).get('id', '')),
                        publish_time=comment_data.get('created_at', ''),
                        like_count=self._safe_int(comment_data.get('fav_count')),
                        reply_count=self._safe_int(comment_data.get('reply_count'))
                    )
                    comments.append(comment)
                except:
                    continue

        except Exception as e:
            logger.error(f"JSON评论提取失败: {e}")

        return comments

    def _extract_comments_from_html(self, soup: BeautifulSoup) -> List[CommentData]:
        """从HTML页面中提取评论 - 专注HTML解析"""
        comments = []
        try:
            selectors = self.selectors.get("comments", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到评论容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到评论容器，在整个页面中查找")

            # 查找评论项目
            comment_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    comment_elements = elements
                    logger.debug(f"找到 {len(elements)} 个评论项目: {item_selector}")
                    break

            # 提取每个评论的信息
            for i, element in enumerate(comment_elements):
                try:
                    # 提取评论内容
                    content = self._extract_text_by_selectors(element, selectors.get("content", []))

                    # 提取作者
                    author = self._extract_text_by_selectors(element, selectors.get("author", []))

                    # 提取时间
                    publish_time = self._extract_text_by_selectors(element, selectors.get("time", []))

                    # 提取点赞数
                    like_count = self._extract_number_by_selectors(element, selectors.get("like_count", []))

                    # 如果至少有评论内容，就创建记录
                    if content and content.strip():
                        comment = CommentData(
                            comment_id=str(i + 1),  # 简单的ID生成
                            content=content.strip(),
                            author=author or '',
                            publish_time=publish_time or '',
                            like_count=self._safe_int(like_count)
                        )
                        comments.append(comment)
                        logger.debug(f"提取评论: {author} - {content[:50]}...")

                except Exception as e:
                    logger.debug(f"提取单个评论失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"HTML评论提取失败: {e}")

        logger.info(f"成功提取 {len(comments)} 条评论")
        return comments

    def _extract_stock_news(self, html_content: str) -> List[HotTopicData]:
        """提取个股相关新闻"""
        news = []
        try:
            # 尝试从JavaScript数据中提取
            js_pattern = r'newsData["\']?\s*:\s*(\[.*?\])'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    news_data = json.loads(match.group(1))
                    for item in news_data:
                        news_item = HotTopicData(
                            title=item.get('title', ''),
                            url=item.get('url', ''),
                            publish_time=item.get('created_at', ''),
                            summary=item.get('summary', '')
                        )
                        news.append(news_item)
                except:
                    pass

        except Exception as e:
            logger.error(f"个股新闻提取失败: {e}")

        return news

    def _extract_financial_data(self, html_content: str) -> Dict[str, Any]:
        """提取财务数据"""
        financial_data = {}
        try:
            # 尝试从JavaScript数据中提取财务指标
            js_pattern = r'financialData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    data = json.loads(match.group(1))
                    financial_data.update(data)
                except:
                    pass

        except Exception as e:
            logger.error(f"财务数据提取失败: {e}")

        return financial_data

    def _extract_technical_indicators(self, html_content: str) -> Dict[str, Any]:
        """提取技术指标"""
        technical_indicators = {}
        try:
            # 尝试从JavaScript数据中提取技术指标
            js_pattern = r'technicalData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    data = json.loads(match.group(1))
                    technical_indicators.update(data)
                except:
                    pass

        except Exception as e:
            logger.error(f"技术指标提取失败: {e}")

        return technical_indicators

    def _is_json_response(self, content: str) -> bool:
        """判断是否为JSON响应"""
        try:
            json.loads(content)
            return True
        except:
            return False

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_stock_data(data: StockData) -> bool:
        """验证股票数据的完整性"""
        if not data:
            return False
            
        # 必须有股票代码和名称
        if not data.symbol or not data.name:
            return False
            
        # 价格数据应该合理
        if data.current_price is not None and data.current_price <= 0:
            return False
            
        return True
    
    @staticmethod
    def clean_stock_data(data: StockData) -> StockData:
        """清理股票数据"""
        if not data:
            return data
            
        # 清理股票代码
        if data.symbol:
            data.symbol = data.symbol.strip().upper()
            
        # 清理股票名称
        if data.name:
            data.name = data.name.strip()
            
        return data

class MultiSourceExtractor:
    """多数据源提取器"""
    
    def __init__(self):
        self.xueqiu_extractor = XueqiuExtractor()
        self.validator = DataValidator()
        
    def extract(self, html_content: str, url: str = None, source: str = 'xueqiu') -> Optional[StockData]:
        """从多个数据源提取数据"""
        try:
            if source == 'xueqiu':
                data = self.xueqiu_extractor.extract_from_html(html_content, url)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
                
            if data and self.validator.validate_stock_data(data):
                return self.validator.clean_stock_data(data)
                
            return None
            
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            return None

    def extract_homepage(self, html_content: str, source: str = 'xueqiu') -> Optional[XueqiuHomePageData]:
        """提取首页数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_homepage_data(html_content)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
        except Exception as e:
            logger.error(f"首页数据提取失败: {e}")
            return None

    def extract_stock_detail(self, html_content: str, url: str = None, source: str = 'xueqiu') -> Optional[StockDetailData]:
        """提取个股详细数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_stock_detail(html_content, url)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
        except Exception as e:
            logger.error(f"个股详细数据提取失败: {e}")
            return None

    def extract_comments(self, html_content: str, source: str = 'xueqiu') -> List[CommentData]:
        """提取评论数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_stock_comments(html_content)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return []
        except Exception as e:
            logger.error(f"评论数据提取失败: {e}")
            return []
