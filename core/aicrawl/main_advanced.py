"""
高级爬虫主程序 - 集成完整的反爬虫功能
"""
import asyncio
import logging
import json
import argparse
from typing import List, Dict, Any
from pathlib import Path

from crawler_engine import SmartCrawler, CrawlResult
from extractors import XueqiuHomePageData, StockDetailData, CommentData
from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CrawlerApp:
    """爬虫应用主类"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = SmartCrawler(enable_anti_detection)
        self.results = []
        
    async def crawl_single_stock(self, symbol: str) -> Dict[str, Any]:
        """爬取单个股票"""
        logger.info(f"开始爬取股票: {symbol}")
        
        result = await self.crawler.crawl_stock(symbol)
        
        if result.success and result.data:
            logger.info(f"成功爬取 {symbol}: {result.data.name} - ¥{result.data.current_price}")
            return self._format_result(result)
        else:
            logger.error(f"爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "error": result.error, "success": False}
    
    async def crawl_multiple_stocks(self, symbols: List[str], 
                                  concurrent_limit: int = 3) -> List[Dict[str, Any]]:
        """批量爬取股票"""
        logger.info(f"开始批量爬取 {len(symbols)} 个股票")
        
        results = await self.crawler.crawl_multiple_stocks(
            symbols, 
            concurrent_limit=concurrent_limit
        )
        
        formatted_results = []
        for result in results:
            formatted_results.append(self._format_result(result))
            
        # 打印性能报告
        report = self.crawler.get_performance_report()
        logger.info(f"爬取完成 - 性能报告: {report}")
        
        return formatted_results
    
    def _format_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化结果"""
        if result.success and result.data:
            return {
                "success": True,
                "symbol": result.data.symbol,
                "name": result.data.name,
                "current_price": result.data.current_price,
                "change": result.data.change,
                "change_percent": result.data.change_percent,
                "volume": result.data.volume,
                "market_cap": result.data.market_cap,
                "pe_ttm": result.data.pe_ttm,
                "pb": result.data.pb,
                "response_time": result.response_time,
                "url": result.url
            }
        else:
            return {
                "success": False,
                "error": result.error,
                "url": result.url,
                "response_time": result.response_time
            }
    
    def save_results(self, results: List[Dict[str, Any]], filename: str = None):
        """保存结果到文件"""
        if not filename:
            filename = f"crawl_results_{int(asyncio.get_event_loop().time())}.json"
            
        output_path = Path(filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {output_path}")
        
        # 同时保存为CSV格式
        self._save_as_csv(results, output_path.with_suffix('.csv'))
    
    def _save_as_csv(self, results: List[Dict[str, Any]], csv_path: Path):
        """保存为CSV格式"""
        try:
            import pandas as pd
            
            # 过滤成功的结果
            successful_results = [r for r in results if r.get('success')]
            
            if successful_results:
                df = pd.DataFrame(successful_results)
                df.to_csv(csv_path, index=False, encoding='utf-8')
                logger.info(f"CSV结果已保存到: {csv_path}")
        except ImportError:
            logger.warning("pandas未安装，跳过CSV保存")

    async def crawl_homepage(self) -> Dict[str, Any]:
        """爬取雪球首页"""
        logger.info("开始爬取雪球首页")

        result = await self.crawler.crawl_homepage()

        if result.success and result.data:
            logger.info("成功爬取首页数据")
            return self._format_homepage_result(result)
        else:
            logger.error(f"首页爬取失败: {result.error}")
            return {"success": False, "error": result.error}

    async def crawl_stock_detail(self, symbol: str) -> Dict[str, Any]:
        """爬取个股详细信息"""
        logger.info(f"开始爬取股票详细信息: {symbol}")

        result = await self.crawler.crawl_stock_detail(symbol)

        if result.success and result.data:
            logger.info(f"成功爬取 {symbol} 详细信息")
            return self._format_detail_result(result)
        else:
            logger.error(f"详细信息爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "success": False, "error": result.error}

    async def crawl_stock_comments(self, symbol: str, count: int = 10) -> Dict[str, Any]:
        """爬取个股评论"""
        logger.info(f"开始爬取股票评论: {symbol}")

        result = await self.crawler.crawl_stock_comments(symbol, count)

        if result.success and result.data:
            logger.info(f"成功爬取 {symbol} 评论 {len(result.data)} 条")
            return self._format_comments_result(result, symbol)
        else:
            logger.error(f"评论爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "success": False, "error": result.error}

    async def crawl_comprehensive_data(self, symbol: str) -> Dict[str, Any]:
        """综合爬取个股所有数据"""
        logger.info(f"开始综合爬取股票数据: {symbol}")

        results = await self.crawler.crawl_comprehensive_stock_data(symbol)

        formatted_results = {}
        for data_type, result in results.items():
            if result.success:
                if data_type == 'basic':
                    formatted_results[data_type] = self._format_result(result)
                elif data_type == 'detail':
                    formatted_results[data_type] = self._format_detail_result(result)
                elif data_type == 'comments':
                    formatted_results[data_type] = self._format_comments_result(result, symbol)
            else:
                formatted_results[data_type] = {"success": False, "error": result.error}

        return {
            "symbol": symbol,
            "success": True,
            "data": formatted_results,
            "summary": self._create_comprehensive_summary(formatted_results)
        }

    def _format_homepage_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化首页结果"""
        data = result.data
        return {
            "success": True,
            "data_type": "homepage",
            "hot_stocks_count": len(data.hot_stocks) if data.hot_stocks else 0,
            "hot_topics_count": len(data.hot_topics) if data.hot_topics else 0,
            "hot_stocks": [
                {
                    "symbol": stock.symbol,
                    "name": stock.name,
                    "current_price": stock.current_price,
                    "change_percent": stock.change_percent,
                    "rank": stock.rank
                } for stock in (data.hot_stocks[:5] if data.hot_stocks else [])
            ],
            "hot_topics": [
                {
                    "title": topic.title,
                    "author": topic.author,
                    "view_count": topic.view_count,
                    "comment_count": topic.comment_count
                } for topic in (data.hot_topics[:5] if data.hot_topics else [])
            ],
            "market_summary": data.market_summary,
            "crawl_time": data.crawl_time,
            "response_time": result.response_time
        }

    def _format_detail_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化详细信息结果"""
        data = result.data
        return {
            "success": True,
            "data_type": "detail",
            "basic_info": self._format_result(CrawlResult(
                success=True,
                data=data.basic_info,
                response_time=result.response_time
            )) if data.basic_info else None,
            "comments_count": len(data.comments) if data.comments else 0,
            "news_count": len(data.news) if data.news else 0,
            "financial_data": data.financial_data,
            "technical_indicators": data.technical_indicators,
            "response_time": result.response_time
        }

    def _format_comments_result(self, result: CrawlResult, symbol: str) -> Dict[str, Any]:
        """格式化评论结果"""
        comments = result.data
        return {
            "success": True,
            "symbol": symbol,
            "data_type": "comments",
            "total_comments": len(comments),
            "comments": [
                {
                    "content": comment.content[:200] + "..." if len(comment.content) > 200 else comment.content,
                    "author": comment.author,
                    "publish_time": comment.publish_time,
                    "like_count": comment.like_count,
                    "reply_count": comment.reply_count
                } for comment in comments[:10]  # 只显示前10条
            ],
            "response_time": result.response_time
        }

    def _create_comprehensive_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """创建综合数据摘要"""
        summary = {
            "data_types_collected": list(results.keys()),
            "success_count": sum(1 for r in results.values() if r.get('success')),
            "total_count": len(results)
        }

        # 添加具体数据统计
        if 'detail' in results and results['detail'].get('success'):
            detail = results['detail']
            summary['comments_count'] = detail.get('comments_count', 0)
            summary['news_count'] = detail.get('news_count', 0)

        if 'comments' in results and results['comments'].get('success'):
            summary['total_comments'] = results['comments'].get('total_comments', 0)

        return summary

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='雪球股票数据爬虫')
    parser.add_argument('--symbols', nargs='+', default=['SH688627'], 
                       help='股票代码列表')
    parser.add_argument('--concurrent', type=int, default=3,
                       help='并发数量')
    parser.add_argument('--output', type=str, 
                       help='输出文件名')
    parser.add_argument('--disable-anti-detection', action='store_true',
                       help='禁用反检测功能')
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式，只爬取一个股票')
    parser.add_argument('--mode', choices=['stock', 'homepage', 'detail', 'comments', 'comprehensive'],
                       default='stock', help='爬取模式')
    parser.add_argument('--comment-count', type=int, default=10,
                       help='爬取评论数量')

    args = parser.parse_args()
    
    # 创建爬虫应用
    app = CrawlerApp(enable_anti_detection=not args.disable_anti_detection)
    
    try:
        if args.mode == 'homepage':
            # 首页爬取模式
            logger.info("运行首页爬取模式")
            result = await app.crawl_homepage()
            print(json.dumps(result, ensure_ascii=False, indent=2))
            if args.output:
                app.save_results([result], args.output)

        elif args.mode == 'detail':
            # 详细信息爬取模式
            logger.info("运行详细信息爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_stock_detail(symbol)
                results.append(result)
                print(f"详细信息爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    print(f"✓ {result.get('symbol', 'Unknown')}: 详细信息已获取")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: {result.get('error')}")

        elif args.mode == 'comments':
            # 评论爬取模式
            logger.info("运行评论爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_stock_comments(symbol, args.comment_count)
                results.append(result)
                print(f"评论爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    print(f"✓ {result.get('symbol', 'Unknown')}: 获取 {result.get('total_comments', 0)} 条评论")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: {result.get('error')}")

        elif args.mode == 'comprehensive':
            # 综合爬取模式
            logger.info("运行综合爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_comprehensive_data(symbol)
                results.append(result)
                print(f"综合数据爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    summary = result.get('summary', {})
                    print(f"✓ {result.get('symbol', 'Unknown')}: 收集 {summary.get('success_count', 0)}/{summary.get('total_count', 0)} 类数据")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: 综合爬取失败")

        elif args.test_mode:
            # 测试模式
            logger.info("运行测试模式")
            result = await app.crawl_single_stock(args.symbols[0])
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            # 默认股票爬取模式
            results = await app.crawl_multiple_stocks(
                args.symbols,
                concurrent_limit=args.concurrent
            )

            # 打印结果摘要
            successful_count = sum(1 for r in results if r.get('success'))
            logger.info(f"爬取完成: {successful_count}/{len(results)} 成功")

            # 保存结果
            if args.output:
                app.save_results(results, args.output)

            # 打印部分结果
            for result in results[:5]:  # 只打印前5个结果
                if result.get('success'):
                    print(f"{result['symbol']} - {result['name']}: ¥{result['current_price']} ({result['change_percent']:+.2f}%)")
                else:
                    print(f"{result.get('symbol', 'Unknown')}: 爬取失败 - {result.get('error')}")
                    
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")
        raise

# 预定义的股票列表
POPULAR_STOCKS = [
    'SH688627',  # 精智达
    'SZ000001',  # 平安银行
    'SH600036',  # 招商银行
    'SZ000002',  # 万科A
    'SH600519',  # 贵州茅台
    'SZ000858',  # 五粮液
    'SH600276',  # 恒瑞医药
    'SZ002415',  # 海康威视
    'SH600887',  # 伊利股份
    'SZ000725'   # 京东方A
]

async def demo_crawl():
    """演示爬取 - 展示所有新功能"""
    print("=== 雪球股票爬虫升级版演示 ===")

    app = CrawlerApp(enable_anti_detection=True)

    # 1. 演示首页爬取
    print("\n1. 首页热门信息爬取演示")
    print("-" * 40)
    homepage_result = await app.crawl_homepage()
    if homepage_result.get('success'):
        print(f"✓ 首页数据爬取成功")
        print(f"  热门股票: {homepage_result.get('hot_stocks_count', 0)} 个")
        print(f"  热门话题: {homepage_result.get('hot_topics_count', 0)} 个")

        # 显示部分热门股票
        hot_stocks = homepage_result.get('hot_stocks', [])
        if hot_stocks:
            print("  前3个热门股票:")
            for stock in hot_stocks[:3]:
                print(f"    {stock['symbol']} - {stock['name']}: {stock.get('change_percent', 'N/A')}%")
    else:
        print(f"✗ 首页爬取失败: {homepage_result.get('error')}")

    # 2. 演示个股详细信息爬取
    print("\n2. 个股详细信息爬取演示")
    print("-" * 40)
    demo_symbol = POPULAR_STOCKS[0]
    detail_result = await app.crawl_stock_detail(demo_symbol)
    if detail_result.get('success'):
        print(f"✓ {demo_symbol} 详细信息爬取成功")
        print(f"  评论数量: {detail_result.get('comments_count', 0)}")
        print(f"  新闻数量: {detail_result.get('news_count', 0)}")

        basic_info = detail_result.get('basic_info')
        if basic_info and basic_info.get('success'):
            print(f"  股票名称: {basic_info.get('name', 'N/A')}")
            print(f"  当前价格: ¥{basic_info.get('current_price', 'N/A')}")
    else:
        print(f"✗ {demo_symbol} 详细信息爬取失败: {detail_result.get('error')}")

    # 3. 演示评论爬取
    print("\n3. 个股评论爬取演示")
    print("-" * 40)
    comments_result = await app.crawl_stock_comments(demo_symbol, count=5)
    if comments_result.get('success'):
        print(f"✓ {demo_symbol} 评论爬取成功")
        print(f"  总评论数: {comments_result.get('total_comments', 0)}")

        comments = comments_result.get('comments', [])
        if comments:
            print("  最新评论预览:")
            for i, comment in enumerate(comments[:2], 1):
                content = comment['content'][:50] + "..." if len(comment['content']) > 50 else comment['content']
                print(f"    {i}. {comment['author']}: {content}")
    else:
        print(f"✗ {demo_symbol} 评论爬取失败: {comments_result.get('error')}")

    # 4. 演示综合爬取
    print("\n4. 综合数据爬取演示")
    print("-" * 40)
    comprehensive_result = await app.crawl_comprehensive_data(demo_symbol)
    if comprehensive_result.get('success'):
        print(f"✓ {demo_symbol} 综合数据爬取成功")
        summary = comprehensive_result.get('summary', {})
        print(f"  数据类型: {', '.join(summary.get('data_types_collected', []))}")
        print(f"  成功率: {summary.get('success_count', 0)}/{summary.get('total_count', 0)}")
    else:
        print(f"✗ {demo_symbol} 综合爬取失败")

    # 性能报告
    print("\n=== 性能报告 ===")
    report = app.crawler.get_performance_report()
    for key, value in report.items():
        print(f"{key}: {value}")

    print("\n=== 使用说明 ===")
    print("命令行使用示例:")
    print("  python main_advanced.py --mode homepage                    # 爬取首页")
    print("  python main_advanced.py --mode detail --symbols SH688627  # 爬取个股详情")
    print("  python main_advanced.py --mode comments --symbols SH688627 # 爬取评论")
    print("  python main_advanced.py --mode comprehensive --symbols SH688627 # 综合爬取")

if __name__ == "__main__":
    # 如果没有命令行参数，运行演示
    import sys
    if len(sys.argv) == 1:
        asyncio.run(demo_crawl())
    else:
        asyncio.run(main())
