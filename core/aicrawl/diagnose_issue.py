#!/usr/bin/env python3
"""
诊断雪球爬虫数据提取问题
"""
import asyncio
import json
from bs4 import BeautifulSoup

async def diagnose_crawling_issue():
    """诊断爬虫问题"""
    print("🔍 雪球爬虫问题诊断")
    print("=" * 60)
    
    from crawler_engine import XueqiuCrawler
    from extractors import XueqiuExtractor
    
    # 1. 测试页面访问
    print("\n📡 步骤1: 测试页面访问")
    print("-" * 40)
    
    crawler = XueqiuCrawler(enable_anti_detection=True)
    result = await crawler.crawl_homepage()
    
    if not result.success:
        print(f"❌ 页面访问失败: {result.error}")
        return
    
    print(f"✅ 页面访问成功")
    print(f"   响应时间: {result.response_time:.2f}秒")
    print(f"   页面大小: {len(result.raw_html)} 字符")
    
    # 保存页面用于分析
    with open('diagnose_page.html', 'w', encoding='utf-8') as f:
        f.write(result.raw_html)
    print(f"   页面已保存到: diagnose_page.html")
    
    # 2. 分析页面内容
    print("\n🔍 步骤2: 分析页面内容")
    print("-" * 40)
    
    soup = BeautifulSoup(result.raw_html, 'html.parser')
    
    # 检查页面标题
    title = soup.title.text if soup.title else "无标题"
    print(f"页面标题: {title}")
    
    # 检查是否包含关键词
    keywords = ['热门', '股票', '话题', 'hot', 'stock']
    for keyword in keywords:
        count = result.raw_html.lower().count(keyword.lower())
        print(f"包含 '{keyword}': {count} 次")
    
    # 检查是否有JavaScript错误或重定向
    if '403' in result.raw_html or 'forbidden' in result.raw_html.lower():
        print("⚠️  可能遇到403禁止访问")
    if 'captcha' in result.raw_html.lower() or '验证码' in result.raw_html:
        print("⚠️  可能遇到验证码")
    if 'login' in result.raw_html.lower() or '登录' in result.raw_html:
        print("⚠️  可能需要登录")
    
    # 3. 测试选择器
    print("\n🎯 步骤3: 测试选择器")
    print("-" * 40)
    
    from config import ExtractionConfig
    selectors = ExtractionConfig.XUEQIU_SELECTORS
    
    # 测试热门股票选择器
    print("热门股票选择器测试:")
    stock_selectors = selectors.get('homepage_hot_stocks', {})
    for container_selector in stock_selectors.get('container', []):
        elements = soup.select(container_selector)
        if elements:
            print(f"  ✅ {container_selector}: 找到 {len(elements)} 个容器")
            
            # 测试项目选择器
            container = elements[0]
            for item_selector in stock_selectors.get('item', []):
                items = container.select(item_selector)
                if items:
                    print(f"    ✅ {item_selector}: 找到 {len(items)} 个项目")
                    break
            else:
                print(f"    ❌ 未找到任何项目")
            break
        else:
            print(f"  ❌ {container_selector}: 未找到")
    
    # 测试热门话题选择器
    print("\n热门话题选择器测试:")
    topic_selectors = selectors.get('homepage_hot_topics', {})
    for container_selector in topic_selectors.get('container', []):
        elements = soup.select(container_selector)
        if elements:
            print(f"  ✅ {container_selector}: 找到 {len(elements)} 个容器")
            
            # 测试项目选择器
            container = elements[0]
            for item_selector in topic_selectors.get('item', []):
                items = container.select(item_selector)
                if items:
                    print(f"    ✅ {item_selector}: 找到 {len(items)} 个项目")
                    break
            else:
                print(f"    ❌ 未找到任何项目")
            break
        else:
            print(f"  ❌ {container_selector}: 未找到")
    
    # 4. 测试提取器
    print("\n⚙️ 步骤4: 测试数据提取")
    print("-" * 40)
    
    extractor = XueqiuExtractor()
    
    # 测试热门股票提取
    hot_stocks = extractor._extract_hot_stocks(soup, result.raw_html)
    print(f"热门股票提取结果: {len(hot_stocks)} 个")
    if hot_stocks:
        for i, stock in enumerate(hot_stocks[:3]):
            print(f"  {i+1}. {stock.symbol} - {stock.name}: {stock.change_percent}%")
    
    # 测试热门话题提取
    hot_topics = extractor._extract_hot_topics(soup, result.raw_html)
    print(f"热门话题提取结果: {len(hot_topics)} 个")
    if hot_topics:
        for i, topic in enumerate(hot_topics[:3]):
            print(f"  {i+1}. {topic.title}")
    
    # 5. 完整测试
    print("\n🧪 步骤5: 完整功能测试")
    print("-" * 40)
    
    homepage_data = extractor.extract_homepage_data(result.raw_html)
    if homepage_data:
        print(f"✅ 完整提取成功:")
        print(f"   热门股票: {len(homepage_data.hot_stocks)} 个")
        print(f"   热门话题: {len(homepage_data.hot_topics)} 个")
        
        # 保存结果
        result_data = {
            "hot_stocks_count": len(homepage_data.hot_stocks),
            "hot_topics_count": len(homepage_data.hot_topics),
            "hot_stocks": [
                {
                    "symbol": stock.symbol,
                    "name": stock.name,
                    "change_percent": stock.change_percent,
                    "rank": stock.rank
                } for stock in homepage_data.hot_stocks[:5]
            ],
            "hot_topics": [
                {
                    "title": topic.title,
                    "url": topic.url
                } for topic in homepage_data.hot_topics[:5]
            ]
        }
        
        with open('diagnose_result.json', 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        print(f"   结果已保存到: diagnose_result.json")
    else:
        print(f"❌ 完整提取失败")
    
    # 6. 建议
    print("\n💡 诊断建议")
    print("-" * 40)
    
    if not hot_stocks and not hot_topics:
        print("🔧 可能的解决方案:")
        print("1. 检查网络连接是否正常")
        print("2. 尝试使用VPN或更换网络环境")
        print("3. 增加请求间隔，避免触发反爬虫")
        print("4. 检查是否需要更新User-Agent")
        print("5. 查看保存的HTML文件，确认页面内容")
        
        print("\n🛠️ 调试命令:")
        print("# 查看页面内容")
        print("cat diagnose_page.html | head -100")
        print("\n# 搜索关键元素")
        print("grep -i 'hot\\|股票\\|话题' diagnose_page.html")
        
    elif hot_stocks or hot_topics:
        print("✅ 数据提取正常，可能是偶发性问题")
        print("💡 建议:")
        print("1. 重新运行爬虫")
        print("2. 适当增加请求间隔")
        print("3. 检查网络稳定性")
    
    print(f"\n📊 诊断完成")

if __name__ == "__main__":
    asyncio.run(diagnose_crawling_issue())
