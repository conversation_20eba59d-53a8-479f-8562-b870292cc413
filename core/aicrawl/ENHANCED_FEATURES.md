# 雪球网站爬虫升级功能说明

## 🚀 新增功能概览

本次升级为雪球网站爬虫添加了全面的整站内容爬取功能，支持首页热门信息、个股详细页面和评论等多种数据类型的采集。

### ✨ 主要新功能

1. **首页热门信息爬取** - 获取雪球首页的热门股票、热门话题和市场概况
2. **个股详细信息爬取** - 深度采集个股的详细数据、财务指标和技术分析
3. **个股评论爬取** - 采集用户评论、讨论和情感分析数据
4. **综合数据爬取** - 一次性获取个股的所有相关数据
5. **多种数据格式支持** - 支持JSON和CSV格式输出

## 📊 新增数据结构

### HotTopicData - 热门话题数据
```python
@dataclass
class HotTopicData:
    title: str = ""           # 话题标题
    url: str = ""            # 话题链接
    view_count: int = None   # 浏览量
    comment_count: int = None # 评论数
    author: str = ""         # 作者
    publish_time: str = None # 发布时间
    summary: str = ""        # 摘要
    tags: List[str] = None   # 标签
```

### HotStockData - 热门股票数据
```python
@dataclass
class HotStockData:
    symbol: str = ""              # 股票代码
    name: str = ""               # 股票名称
    current_price: float = None  # 当前价格
    change_percent: float = None # 涨跌幅
    volume: int = None          # 成交量
    reason: str = ""            # 上榜原因
    rank: int = None            # 排名
    heat_score: float = None    # 热度评分
```

### CommentData - 评论数据
```python
@dataclass
class CommentData:
    comment_id: str = ""         # 评论ID
    content: str = ""           # 评论内容
    author: str = ""            # 作者
    author_id: str = ""         # 作者ID
    publish_time: str = None    # 发布时间
    like_count: int = None      # 点赞数
    reply_count: int = None     # 回复数
    parent_id: str = None       # 父评论ID
    sentiment: str = None       # 情感分析结果
```

## 🛠️ 使用方法

### 命令行使用

#### 1. 首页热门信息爬取
```bash
python main_advanced.py --mode homepage
```

#### 2. 个股详细信息爬取
```bash
python main_advanced.py --mode detail --symbols SH688775
```

#### 3. 个股评论爬取
```bash
python main_advanced.py --mode comments --symbols SH688775 --comment-count 20
```

#### 4. 综合数据爬取
```bash
python main_advanced.py --mode comprehensive --symbols SH688775
```

#### 5. 批量处理多个股票
```bash
python main_advanced.py --mode detail --symbols SH688775 SZ000001 SH600036
```

### 编程接口使用

```python
from main_advanced import CrawlerApp
import asyncio

async def example_usage():
    app = CrawlerApp(enable_anti_detection=True)
    
    # 爬取首页数据
    homepage_data = await app.crawl_homepage()
    
    # 爬取个股详细信息
    detail_data = await app.crawl_stock_detail("SH688775")
    
    # 爬取评论
    comments_data = await app.crawl_stock_comments("SH688775", count=10)
    
    # 综合爬取
    comprehensive_data = await app.crawl_comprehensive_data("SH688775")

# 运行示例
asyncio.run(example_usage())
```

## 📋 配置说明

### 新增URL配置
```python
XUEQIU_CONFIG = {
    "homepage_url": "https://xueqiu.com",
    "api_endpoints": {
        "comments": "https://api.xueqiu.com/query/v1/symbol/search/status.json",
        "hot_stocks": "https://stock.xueqiu.com/v5/stock/screener/quote/list.json",
        "hot_topics": "https://api.xueqiu.com/statuses/hot/listV2.json",
        "financial": "https://stock.xueqiu.com/v5/stock/finance/cn/income.json",
        "indicator": "https://stock.xueqiu.com/v5/stock/finance/cn/indicator.json"
    }
}
```

## 🧪 测试功能

运行测试脚本验证所有新功能：

```bash
python test_enhanced_crawler.py
```

测试脚本将依次测试：
- 首页爬取功能
- 个股详细信息爬取
- 评论爬取功能
- 综合数据爬取

## 📈 输出示例

### 首页数据输出示例
```json
{
  "success": true,
  "data_type": "homepage",
  "hot_stocks_count": 10,
  "hot_topics_count": 15,
  "hot_stocks": [
    {
      "symbol": "SH688775",
      "name": "科创板股票",
      "current_price": 45.67,
      "change_percent": 2.34,
      "rank": 1
    }
  ],
  "hot_topics": [
    {
      "title": "市场热点话题",
      "author": "用户名",
      "view_count": 1234,
      "comment_count": 56
    }
  ]
}
```

### 评论数据输出示例
```json
{
  "success": true,
  "symbol": "SH688775",
  "data_type": "comments",
  "total_comments": 25,
  "comments": [
    {
      "content": "这只股票很有潜力...",
      "author": "投资者A",
      "publish_time": "2024-01-15T10:30:00",
      "like_count": 12,
      "reply_count": 3
    }
  ]
}
```

## ⚡ 性能优化

1. **智能延迟调整** - 根据成功率自动调整请求间隔
2. **并发控制** - 支持自定义并发数量
3. **错误重试** - 智能重试机制
4. **缓存优化** - 避免重复请求
5. **反爬虫策略** - 动态User-Agent、代理池等

## 🔧 故障排除

### 常见问题

1. **爬取失败** - 检查网络连接和反爬虫设置
2. **数据为空** - 可能是页面结构变化，需要更新选择器
3. **请求过快** - 增加延迟时间或减少并发数

### 调试模式
```bash
python main_advanced.py --test-mode --symbols SH688775
```

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 新增首页热门信息爬取
- ✅ 新增个股详细信息爬取
- ✅ 新增评论数据爬取
- ✅ 新增综合数据爬取模式
- ✅ 扩展数据模型支持多种数据类型
- ✅ 优化命令行界面
- ✅ 添加完整的测试套件

### v1.0.0 (原版本)
- ✅ 基础股票数据爬取
- ✅ 反爬虫检测
- ✅ 批量处理功能
