# 雪球网站HTML爬虫安全指南

## 🛡️ 为什么选择HTML解析而不是API

雪球网站具有强大的反爬虫策略，包括：
- IP封禁检测
- API访问频率限制
- 动态token验证
- 用户行为分析

**HTML页面解析的优势：**
- 更接近真实用户行为
- 避免API接口的严格限制
- 可以获取页面上的所有可见信息
- 更难被检测为爬虫行为

## 🎯 安全爬取策略

### 1. 请求频率控制
```python
# 推荐的请求间隔
REQUEST_DELAYS = {
    "min_delay": 3,      # 最小延迟3秒
    "max_delay": 10,     # 最大延迟10秒
    "burst_delay": 20,   # 突发请求后延迟20秒
    "error_delay": 60    # 错误后延迟60秒
}
```

### 2. User-Agent轮换
```python
# 使用真实浏览器的User-Agent
USER_AGENTS = [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
]
```

### 3. 页面加载策略
```python
# 等待页面完全加载
crawl_settings = {
    "wait_for": "networkidle",  # 等待网络空闲
    "delay": 3,                 # 额外等待3秒
    "enable_javascript": True,  # 启用JavaScript
    "page_load_timeout": 30     # 页面加载超时30秒
}
```

## 📋 HTML选择器配置

### 个股页面选择器
```python
STOCK_SELECTORS = {
    "name": [
        "h1.stock-name",           # 主要选择器
        ".stock-info h1",          # 备用选择器1
        ".quote-info .name",       # 备用选择器2
        "h1"                       # 通用选择器
    ],
    "price": [
        ".stock-current",
        ".current-price", 
        ".quote-price",
        ".price-current"
    ]
}
```

### 首页选择器
```python
HOMEPAGE_SELECTORS = {
    "hot_stocks": {
        "container": [".hot-stocks", ".rank-list"],
        "item": [".stock-item", ".rank-item"],
        "symbol": [".symbol", ".code"],
        "name": [".name", ".stock-name"]
    }
}
```

## 🔧 使用方法

### 命令行使用
```bash
# 基础股票信息爬取（最安全）
python main_advanced.py --symbols SH688775 --test-mode

# 首页信息爬取
python main_advanced.py --mode homepage

# 个股详细信息（包含评论）
python main_advanced.py --mode detail --symbols SH688775

# 批量爬取（注意控制频率）
python main_advanced.py --symbols SH688775 SZ000001 --concurrent 1
```

### 编程接口
```python
import asyncio
from main_advanced import CrawlerApp

async def safe_crawl():
    # 启用反检测功能
    app = CrawlerApp(enable_anti_detection=True)
    
    # 单个股票爬取
    result = await app.crawl_single_stock("SH688775")
    
    # 等待足够时间再进行下一次请求
    await asyncio.sleep(5)
    
    # 首页爬取
    homepage = await app.crawl_homepage()

asyncio.run(safe_crawl())
```

## ⚠️ 重要注意事项

### 1. 请求频率
- **最小间隔**: 3秒
- **推荐间隔**: 5-10秒
- **批量爬取**: 并发数设为1
- **错误后**: 等待60秒再重试

### 2. IP保护
- 使用代理池（如果有）
- 避免长时间连续爬取
- 监控错误率，及时停止

### 3. 数据验证
```python
def validate_result(result):
    if not result.get('success'):
        print(f"爬取失败: {result.get('error')}")
        return False
    
    # 检查关键字段
    if not result.get('name') or not result.get('symbol'):
        print("数据不完整，可能被反爬虫拦截")
        return False
    
    return True
```

## 🧪 测试和调试

### 运行测试
```bash
# 运行HTML解析测试
python test_html_crawler.py

# 单个功能测试
python main_advanced.py --mode homepage --test-mode
```

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 保存HTML内容用于分析
with open('debug_page.html', 'w', encoding='utf-8') as f:
    f.write(result.raw_html)
```

## 📊 成功率监控

### 监控指标
- 成功率 > 80%：正常
- 成功率 50-80%：需要调整策略
- 成功率 < 50%：暂停爬取

### 错误处理
```python
def handle_crawl_error(error_msg):
    if "403" in error_msg or "blocked" in error_msg:
        print("可能被封IP，建议暂停1小时")
        return "ip_blocked"
    elif "timeout" in error_msg:
        print("网络超时，可以重试")
        return "timeout"
    else:
        print(f"其他错误: {error_msg}")
        return "unknown"
```

## 🔄 最佳实践

### 1. 渐进式爬取
```python
# 先测试单个页面
result = await app.crawl_single_stock("SH688775")

# 成功后再扩展到多个
if result.get('success'):
    # 等待足够时间
    await asyncio.sleep(10)
    # 爬取下一个
```

### 2. 错误恢复
```python
async def robust_crawl(symbols):
    for symbol in symbols:
        try:
            result = await app.crawl_single_stock(symbol)
            if not result.get('success'):
                # 错误后增加等待时间
                await asyncio.sleep(60)
        except Exception as e:
            print(f"爬取 {symbol} 失败: {e}")
            await asyncio.sleep(60)
```

### 3. 数据持久化
```python
# 及时保存数据
def save_immediately(result, symbol):
    filename = f"data_{symbol}_{int(time.time())}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
```

## 🚨 风险提示

1. **遵守robots.txt**: 检查网站的爬虫协议
2. **合理使用**: 不要对服务器造成过大压力
3. **数据用途**: 仅用于学习和研究目的
4. **法律合规**: 遵守相关法律法规
5. **及时停止**: 发现异常立即停止爬取

## 📞 故障排除

### 常见问题
1. **403错误**: IP被封，更换IP或等待
2. **超时错误**: 网络问题，检查连接
3. **数据为空**: 页面结构变化，更新选择器
4. **验证码**: 需要人工处理或更换策略

### 联系方式
如遇到技术问题，可以：
- 检查日志文件
- 运行测试脚本
- 调整配置参数
- 降低爬取频率
