#!/usr/bin/env python3
"""
测试增强版雪球爬虫功能
"""
import asyncio
import logging
import json
from pathlib import Path

from main_advanced import CrawlerApp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_homepage_crawl():
    """测试首页爬取"""
    print("=" * 50)
    print("测试首页爬取功能")
    print("=" * 50)
    
    app = CrawlerApp(enable_anti_detection=True)
    
    try:
        result = await app.crawl_homepage()
        
        if result.get('success'):
            print("✓ 首页爬取成功")
            print(f"热门股票数量: {result.get('hot_stocks_count', 0)}")
            print(f"热门话题数量: {result.get('hot_topics_count', 0)}")
            
            # 保存结果
            output_file = "homepage_test_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 首页爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 首页爬取异常: {e}")
        return False

async def test_stock_detail_crawl():
    """测试个股详细信息爬取"""
    print("=" * 50)
    print("测试个股详细信息爬取功能")
    print("=" * 50)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"  # 测试股票代码
    
    try:
        result = await app.crawl_stock_detail(test_symbol)
        
        if result.get('success'):
            print(f"✓ {test_symbol} 详细信息爬取成功")
            print(f"评论数量: {result.get('comments_count', 0)}")
            print(f"新闻数量: {result.get('news_count', 0)}")
            
            # 保存结果
            output_file = f"detail_test_result_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ {test_symbol} 详细信息爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 详细信息爬取异常: {e}")
        return False

async def test_comments_crawl():
    """测试评论爬取"""
    print("=" * 50)
    print("测试评论爬取功能")
    print("=" * 50)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"  # 测试股票代码
    
    try:
        result = await app.crawl_stock_comments(test_symbol, count=5)
        
        if result.get('success'):
            print(f"✓ {test_symbol} 评论爬取成功")
            print(f"总评论数: {result.get('total_comments', 0)}")
            
            comments = result.get('comments', [])
            if comments:
                print("评论预览:")
                for i, comment in enumerate(comments[:3], 1):
                    content = comment['content'][:100] + "..." if len(comment['content']) > 100 else comment['content']
                    print(f"  {i}. {comment['author']}: {content}")
            
            # 保存结果
            output_file = f"comments_test_result_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ {test_symbol} 评论爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"✗ 评论爬取异常: {e}")
        return False

async def test_comprehensive_crawl():
    """测试综合爬取"""
    print("=" * 50)
    print("测试综合爬取功能")
    print("=" * 50)
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688775"  # 测试股票代码
    
    try:
        result = await app.crawl_comprehensive_data(test_symbol)
        
        if result.get('success'):
            print(f"✓ {test_symbol} 综合数据爬取成功")
            
            summary = result.get('summary', {})
            print(f"数据类型: {', '.join(summary.get('data_types_collected', []))}")
            print(f"成功率: {summary.get('success_count', 0)}/{summary.get('total_count', 0)}")
            
            # 显示各类数据的状态
            data = result.get('data', {})
            for data_type, data_result in data.items():
                status = "✓" if data_result.get('success') else "✗"
                print(f"  {status} {data_type}: {'成功' if data_result.get('success') else data_result.get('error', '失败')}")
            
            # 保存结果
            output_file = f"comprehensive_test_result_{test_symbol}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ {test_symbol} 综合爬取失败")
            return False
            
    except Exception as e:
        print(f"✗ 综合爬取异常: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    print("开始运行增强版雪球爬虫测试")
    print("测试股票代码: SH688775")
    print()
    
    test_results = []
    
    # 测试首页爬取
    result1 = await test_homepage_crawl()
    test_results.append(("首页爬取", result1))
    
    # 等待一段时间避免请求过快
    await asyncio.sleep(3)
    
    # 测试个股详细信息爬取
    result2 = await test_stock_detail_crawl()
    test_results.append(("个股详细信息", result2))
    
    await asyncio.sleep(3)
    
    # 测试评论爬取
    result3 = await test_comments_crawl()
    test_results.append(("评论爬取", result3))
    
    await asyncio.sleep(3)
    
    # 测试综合爬取
    result4 = await test_comprehensive_crawl()
    test_results.append(("综合爬取", result4))
    
    # 打印测试总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(test_results)} 测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！雪球爬虫升级成功！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
