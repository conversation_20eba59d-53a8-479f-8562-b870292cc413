# -*- coding: utf-8 -*-

"""
雪球热门文章爬虫
爬取雪球平台的热门文章和动态
数据源：xueqiu.com
"""

import time
import random

from crawl.items import MediaItem
from crawl.tools.spider_base import NewsSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_news_processor
from crawl.tools.helper import ago_day_timestr, fetch_quant_raw, today_timestamp, filter_emoji, get_xueqiu_cookie
from scrapy.http import FormRequest


class XueqiuMediaSpider(NewsSpider):
    """
    雪球热门文章爬虫
    爬取雪球平台的热门文章和动态
    """
    name = 'xueqiu-media'
    allowed_domains = ['xueqiu.com']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_processor = create_news_processor()
        self.cookies = get_xueqiu_cookie()
        self.times = 0
        self.max_pages = 10

        # Debug: Log cookie information
        self.logger.info(f"Loaded {len(self.cookies)} cookies for Xueqiu")
        for name, value in self.cookies.items():
            self.logger.debug(f"Cookie: {name} = {value[:20]}{'...' if len(value) > 20 else ''}")

        # 添加更真实的浏览器headers
        self.default_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://xueqiu.com/',
            'Origin': 'https://xueqiu.com'
        }

        try:
            self.quant = fetch_quant_raw()
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None

        # 尝试加载JS上下文，如果失败则跳过MD5验证
        try:
            import execjs
            with open('./test.js', 'r', encoding='utf-8') as f:
                self.ctx = execjs.compile(f.read())
        except Exception as e:
            self.logger.warning(f"Failed to load JS context: {e}")
            self.ctx = None

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 雪球需要保守配置避免被限制"""
        return SpiderConfig(
            concurrent_requests=1,
            download_delay=5,  # 增加延迟避免被检测
            batch_size=5,     # 减少批次大小
            retry_times=3     # 增加重试次数
        )

    def get_start_urls(self) -> list:
        """获取起始URL列表"""
        # 硬编码URL，更直观易读
        timestamp = str(today_timestamp(ms=False))
        return [f'https://xueqiu.com/statuses/hot/listV3.json?source=hot&page=1&_={timestamp}']

    def _get_session_request(self):
        """获取会话初始化请求 - 访问主页建立会话"""
        return FormRequest(
            url='https://xueqiu.com/',
            headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1'
            },
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 30
            },
            cookies=self.cookies,
            callback=self._handle_session_response,
            dont_filter=True
        )
 
    def _add_random_delay(self):
        """添加随机延迟模拟人类行为"""
        delay = random.uniform(2, 8)  # 2-8秒随机延迟
        self.logger.debug(f"Adding random delay: {delay:.2f} seconds")
        time.sleep(delay)

    def _handle_session_response(self, response):
        """处理会话初始化响应"""
        self.logger.info(f"Session initialized with status: {response.status}")

        # 检查是否遇到验证码
        if self._is_captcha_response(response):
            self.logger.error("Captcha detected during session initialization. Aborting.")
            return

        # 添加随机延迟模拟人类行为
        self._add_random_delay()

        # 会话建立后，开始实际的数据请求
        for url in self.get_start_urls():
            # 如果有JS上下文，添加MD5验证
            if self.ctx:
                try:
                    md5 = self.ctx.call('getMd5', url)
                    url = f'{url}&md5__1038={md5}'
                except Exception as e:
                    self.logger.warning(f"Failed to generate MD5: {e}")

            yield FormRequest(
                url=url,
                headers=self.default_headers,
                meta={
                    'cookiejar': 1,
                    'dont_cache': True,
                    'download_timeout': 30,
                    'download_delay': random.uniform(3, 7)  # 每个请求随机延迟
                },
                cookies=self.cookies,
                callback=self.parse_response,
                dont_filter=True
            )

    async def start(self):
        """异步生成初始请求 (替代已弃用的start_requests())"""
        # 首先建立会话
        yield self._get_session_request()

    def start_requests(self):
        """生成初始请求 (已弃用，请使用start()方法)"""
        return super().start_requests()

    def _is_captcha_response(self, response) -> bool:
        """检查是否是验证码页面"""
        if response.status == 302:
            location = response.headers.get('Location', b'').decode('utf-8')
            if 'captcha' in location.lower():
                return True

        # 检查响应体是否包含验证码相关内容
        if hasattr(response, 'text'):
            body_text = response.text.lower()
            captcha_indicators = ['captcha', '验证码', '人机验证', 'robot', 'verification']
            if any(indicator in body_text for indicator in captcha_indicators):
                return True

        return False

    @timing_decorator('xueqiu_media.parse_response')
    def parse_response(self, response):
        """解析雪球媒体响应数据"""
        try:
            # 检查是否遇到验证码
            if self._is_captcha_response(response):
                self.logger.error(f"Captcha detected for {response.url}. Spider may be blocked.")
                self.logger.error(f"Response status: {response.status}")
                self.logger.error(f"Response headers: {dict(response.headers)}")
                # 停止爬取避免进一步被检测
                return

            # 检查是否达到最大页数
            if self.times >= self.max_pages:
                return

            self.times += 1

            json_data = self.parse_json_response(response)
            if not json_data:
                self.logger.warning(f"No JSON data from {response.url}")
                self.logger.warning(f"Response status: {response.status}")
                self.logger.warning(f"Response body preview: {response.text[:500]}")
                return

            if 'list' not in json_data:
                self.logger.warning(f"No 'list' key in response from {response.url}")
                self.logger.warning(f"Response keys: {list(json_data.keys())}")
                if 'error_description' in json_data:
                    self.logger.error(f"API Error: {json_data['error_description']} (Code: {json_data.get('error_code', 'Unknown')})")
                return

            post_list = json_data.get('list', [])
            if not post_list:
                self.logger.info(f"No posts found in {response.url}")
                return

            # 生成下一页请求
            if len(post_list) > 0:
                last_post = post_list[-1]
                last_id = last_post.get('id')
                if last_id:
                    next_url = self._build_next_page_url(last_id)
                    if next_url:
                        yield self._create_next_page_request(next_url)

            # 处理当前页的文章
            time_filter = self._get_time_filter()
            for post in post_list:
                if self._is_valid_post(post, time_filter):
                    item = self._create_media_item(post)
                    if item:
                        yield item

        except Exception as e:
            self.handle_error(e, response)

    def _get_time_filter(self) -> str:
        """获取时间过滤器"""
        try:
            if self.quant and hasattr(self.quant, 'allMediaDay'):
                return ago_day_timestr(self.quant.allMediaDay, '%Y-%m-%d')
            else:
                # 默认获取最近7天的数据
                return ago_day_timestr(7, '%Y-%m-%d')
        except Exception as e:
            self.logger.warning(f"Failed to get time filter: {e}")
            return ago_day_timestr(7, '%Y-%m-%d')

    def _build_next_page_url(self, last_id: str) -> str:
        """构建下一页URL"""
        try:
            timestamp = str(today_timestamp(ms=False))
            return f'https://xueqiu.com/statuses/hot/listV3.json?source=hot&max_id={last_id}&last_id={last_id}&page={self.times}&_={timestamp}'
        except Exception as e:
            self.logger.error(f"Error building next page URL: {e}")
            return None

    def _create_next_page_request(self, url: str) -> FormRequest:
        """创建下一页请求"""
        # 如果有JS上下文，添加MD5验证
        if self.ctx:
            try:
                md5 = self.ctx.call('getMd5', url)
                url = f'{url}&md5__1038={md5}'
            except Exception as e:
                self.logger.warning(f"Failed to generate MD5 for next page: {e}")

        return FormRequest(
            url=url,
            headers=self.default_headers,
            meta={
                'cookiejar': 1,
                'dont_cache': True,
                'download_timeout': 30,
                'download_delay': random.uniform(4, 9)  # 下一页请求更长延迟
            },
            cookies=self.cookies,
            callback=self.parse_response,
            dont_filter=True
        )

    def _is_valid_post(self, post_data: dict, time_filter: str) -> bool:
        """验证文章数据是否有效"""
        try:
            if not post_data.get('id') or not post_data.get('title'):
                return False

            # 检查时间过滤
            created_at = post_data.get('created_at', 0)
            if created_at:
                create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))
                return create_time >= time_filter

            return True
        except Exception as e:
            self.logger.warning(f"Error validating post: {e}")
            return False

    def _create_media_item(self, post_data: dict) -> dict:
        """创建媒体数据项"""
        try:
            # 处理时间
            created_at = post_data.get('created_at', 0)
            create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))

            # 创建数据项
            item = MediaItem()
            item['mediaId'] = str(post_data.get('id', ''))
            item['source'] = 'xueqiu'
            item['title'] = post_data.get('title', '')
            item['content'] = filter_emoji(post_data.get('text', ''))
            item['url'] = f"https://xueqiu.com/{post_data.get('user_id', '')}/{post_data.get('id', '')}"
            item['createTime'] = create_time

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating media item: {e}")
            return None

