import random
import time
import signal
import os
import warnings
from typing import Optional, Union, Dict, Any
from scrapy.http import Request, Response
from scrapy.spiders import <PERSON>
from scrapy.exceptions import NotConfigured
from ..base import BaseMiddleware

# 尝试导入fake_useragent，如果失败则使用备用方案
FAKE_USERAGENT_AVAILABLE = False
try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    pass

class MockUserAgent:
    """模拟UserAgent类，用于当fake_useragent不可用时"""
    def __init__(self, *args, **kwargs):
        # 内置的用户代理列表，模拟fake_useragent的行为
        self.user_agents = [
            # Chrome on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            # Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Firefox on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            # Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            # Edge on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        ]

    @property
    def random(self):
        return random.choice(self.user_agents)

    @property
    def chrome(self):
        chrome_agents = [ua for ua in self.user_agents if 'Chrome' in ua and 'Edg' not in ua]
        return random.choice(chrome_agents)

    @property
    def firefox(self):
        firefox_agents = [ua for ua in self.user_agents if 'Firefox' in ua]
        return random.choice(firefox_agents)

    @property
    def safari(self):
        safari_agents = [ua for ua in self.user_agents if 'Safari' in ua and 'Chrome' not in ua]
        return random.choice(safari_agents) if safari_agents else self.random


class UserAgentMiddleware(BaseMiddleware):
    """随机用户代理中间件 - 迁移自middlewares_old.py"""

    def __init__(self, crawler=None):
        super().__init__(crawler)
        self._last_ua_refresh = time.time()
        self._ua_refresh_interval = 3600  # 1小时刷新一次
        self._network_error_detected = False
        self._init_user_agent()

    def _is_network_error(self, exception):
        """检测是否为网络相关错误"""
        error_str = str(exception).lower()
        network_error_indicators = [
            'network is unreachable',
            'connection refused',
            'timeout',
            'name resolution failed',
            'no route to host',
            'connection timed out',
            'urlopen error',
            'maximum amount of retries reached'
        ]
        return any(indicator in error_str for indicator in network_error_indicators)

    def _init_user_agent(self):
        """初始化用户代理"""
        self.ua = None

        # 更全面的备用用户代理列表
        self.fallback_user_agents = [
            # Chrome on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Chrome on Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Firefox on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            # Firefox on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
            # Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            # Edge on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        ]

        if not FAKE_USERAGENT_AVAILABLE:
            self.logger.warning("fake_useragent library not available, using fallback user agents")
            return

        try:
            # 抑制fake_useragent的警告信息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                # 尝试使用默认初始化，但设置超时来快速失败
                def timeout_handler(signum, frame):
                    raise TimeoutError("UserAgent initialization timeout")

                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(10)  # 10秒超时
                try:
                    self.ua = UserAgent()
                    self.logger.info("User agent initialized successfully with default settings")
                finally:
                    signal.alarm(0)  # 取消超时
                    signal.signal(signal.SIGALRM, old_handler)
        except Exception as e:
            if self._is_network_error(e):
                self._network_error_detected = True
                self.logger.warning(f"Network error detected during UserAgent initialization: {e}")
                self.logger.info("Switching to MockUserAgent due to network issues")
            else:
                self.logger.warning(f"Failed to initialize UserAgent: {e}")
                self.logger.info("Switching to MockUserAgent as fallback")

            try:
                self.ua = MockUserAgent()
                self.logger.info("MockUserAgent initialized successfully")
            except Exception as e2:
                self.logger.error(f"Failed to initialize MockUserAgent: {e2}, using fallback list only")
                self.ua = None

    def _refresh_user_agent_if_needed(self):
        """如果需要，刷新用户代理"""
        current_time = time.time()
        if current_time - self._last_ua_refresh > self._ua_refresh_interval:
            # 如果之前检测到网络错误，跳过刷新
            if self._network_error_detected:
                self.logger.debug("Skipping user agent refresh due to previous network errors")
                self._last_ua_refresh = current_time  # 更新时间戳避免频繁检查
                return

            self.logger.info("Refreshing user agent...")
            self._init_user_agent()
            self._last_ua_refresh = current_time

    def _get_user_agent(self) -> str:
        """获取用户代理"""
        # 定期刷新用户代理
        self._refresh_user_agent_if_needed()

        try:
            if self.ua and hasattr(self.ua, 'random'):
                # 抑制可能的警告
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ua = self.ua.random
                    if ua and len(ua.strip()) > 0:
                        self.stats['user_agent_rotated'] = self.stats.get('user_agent_rotated', 0) + 1
                        return ua
                    else:
                        # 如果返回的UA为空，使用备用
                        self.logger.debug("Empty user agent returned, using fallback")
                        return random.choice(self.fallback_user_agents)
            else:
                # 使用备用用户代理
                return random.choice(self.fallback_user_agents)
        except Exception as e:
            self.logger.warning(f"Error getting user agent: {e}, using fallback")
            return random.choice(self.fallback_user_agents)

    def process_request(self, request: Request, spider: Spider) -> Optional[Request]:
        """处理请求，设置用户代理"""
        self.stats['requests_processed'] = self.stats.get('requests_processed', 0) + 1
        user_agent = self._get_user_agent()
        request.headers['User-Agent'] = user_agent
        self.logger.debug(f"Set User-Agent for {request.url}: {user_agent}")
        return None

    def process_response(self, request: Request, response: Response, spider: Spider) -> Union[Request, Response]:
        """处理响应"""
        self.stats['responses_processed'] = self.stats.get('responses_processed', 0) + 1
        return response

    def process_exception(self, request: Request, exception: Exception, spider: Spider) -> Optional[Request]:
        """处理异常"""
        self.stats['errors'] = self.stats.get('errors', 0) + 1
        self.logger.error(f"Error processing {request.url}: {exception}")
        return None

    def spider_opened(self, spider):
        """爬虫开启时的回调"""
        super().spider_opened(spider)
        if self.ua and not isinstance(self.ua, MockUserAgent):
            ua_status = "fake_useragent (online)"
        elif isinstance(self.ua, MockUserAgent):
            ua_status = "MockUserAgent (offline)" if self._network_error_detected else "MockUserAgent (fallback)"
        else:
            ua_status = "fallback list only"

        self.logger.info(f"UserAgentMiddleware initialized with {ua_status} for spider: {spider.name}")
        self.logger.info(f"Available fallback user agents: {len(self.fallback_user_agents)}")

        if self._network_error_detected:
            self.logger.info("Network connectivity issues detected - using offline user agent rotation")

    def spider_closed(self, spider):
        """爬虫关闭时的回调"""
        super().spider_closed(spider)
        self.logger.info(f"UserAgentMiddleware stats for {spider.name}: {self.get_stats()}")